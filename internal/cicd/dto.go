package cicd

import (
	"fmt"
	"strings"
)

// CreatePipelineRequest 创建流水线请求
type CreatePipelineRequest struct {
	Name        string         `json:"name" validate:"required,min=1,max=100"`
	Description string         `json:"description"`
	AppID       string         `json:"app_id" validate:"required"`
	Config      PipelineConfig `json:"config"`
	TenantID    string         `json:"tenant_id" validate:"required"`
}

// Validate 验证创建流水线请求
func (req *CreatePipelineRequest) Validate() error {
	if strings.TrimSpace(req.Name) == "" {
		return fmt.Errorf("流水线名称不能为空")
	}
	
	if len(req.Name) > 100 {
		return fmt.Errorf("流水线名称长度不能超过100个字符")
	}
	
	if strings.TrimSpace(req.AppID) == "" {
		return fmt.Errorf("应用ID不能为空")
	}
	
	if strings.TrimSpace(req.TenantID) == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	
	// 验证流水线配置
	if err := req.Config.Validate(); err != nil {
		return fmt.E<PERSON><PERSON>("流水线配置验证失败: %w", err)
	}
	
	return nil
}

// UpdatePipelineRequest 更新流水线请求
type UpdatePipelineRequest struct {
	Description *string         `json:"description"`
	Config      *PipelineConfig `json:"config"`
	Status      *PipelineStatus `json:"status"`
}

// CreateBuildRequest 创建构建请求
type CreateBuildRequest struct {
	PipelineID  string `json:"pipeline_id" validate:"required"`
	Branch      string `json:"branch" validate:"required"`
	CommitHash  string `json:"commit_hash" validate:"required"`
	CommitMsg   string `json:"commit_msg"`
	Author      string `json:"author"`
	TriggerType string `json:"trigger_type" validate:"required"` // push, pr, tag, manual, schedule
	TriggerBy   string `json:"trigger_by"`                       // 触发用户ID
	Priority    int    `json:"priority"`                         // 优先级，默认为0
}

// Validate 验证创建构建请求
func (req *CreateBuildRequest) Validate() error {
	if strings.TrimSpace(req.PipelineID) == "" {
		return fmt.Errorf("流水线ID不能为空")
	}
	
	if strings.TrimSpace(req.Branch) == "" {
		return fmt.Errorf("分支名称不能为空")
	}
	
	if strings.TrimSpace(req.CommitHash) == "" {
		return fmt.Errorf("提交哈希不能为空")
	}
	
	validTriggerTypes := []string{"push", "pr", "tag", "manual", "schedule"}
	isValidTrigger := false
	for _, validType := range validTriggerTypes {
		if req.TriggerType == validType {
			isValidTrigger = true
			break
		}
	}
	if !isValidTrigger {
		return fmt.Errorf("无效的触发类型: %s", req.TriggerType)
	}
	
	return nil
}

// CreateDeploymentRequest 创建部署请求
type CreateDeploymentRequest struct {
	AppID       string           `json:"app_id" validate:"required"`
	BuildID     string           `json:"build_id" validate:"required"`
	Environment string           `json:"environment" validate:"required"`
	Version     string           `json:"version" validate:"required"`
	ImageTag    string           `json:"image_tag" validate:"required"`
	Strategy    string           `json:"strategy" validate:"required"` // rolling, blue_green, canary
	Config      DeploymentConfig `json:"config"`
}

// CreateEnvironmentRequest 创建环境请求
type CreateEnvironmentRequest struct {
	Name        string            `json:"name" validate:"required"`
	Description string            `json:"description"`
	Type        string            `json:"type" validate:"required"` // development, staging, production
	Cluster     string            `json:"cluster"`
	Namespace   string            `json:"namespace"`
	Config      EnvironmentConfig `json:"config"`
	TenantID    string            `json:"tenant_id" validate:"required"`
}

// RegisterNodeRequest 注册构建节点请求
type RegisterNodeRequest struct {
	Name     string            `json:"name" validate:"required"`
	Address  string            `json:"address" validate:"required"`
	Capacity int               `json:"capacity" validate:"min=1,max=50"`
	Labels   map[string]string `json:"labels"`
}

// BuildFilter 构建过滤器
type BuildFilter struct {
	AppID       string `json:"app_id"`
	PipelineID  string `json:"pipeline_id"`
	Status      string `json:"status"`
	Branch      string `json:"branch"`
	TriggerType string `json:"trigger_type"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

// DeploymentFilter 部署过滤器
type DeploymentFilter struct {
	AppID       string `json:"app_id"`
	Environment string `json:"environment"`
	Status      string `json:"status"`
	Page        int    `json:"page"`
	PageSize    int    `json:"page_size"`
}

// PipelineResponse 流水线响应
type PipelineResponse struct {
	*Pipeline
	BuildCount    int `json:"build_count"`
	SuccessCount  int `json:"success_count"`
	FailureCount  int `json:"failure_count"`
	LastBuildTime *time.Time `json:"last_build_time"`
}

// BuildResponse 构建响应
type BuildResponse struct {
	*Build
	PipelineName string `json:"pipeline_name"`
	AppName      string `json:"app_name"`
}

// BuildListResponse 构建列表响应
type BuildListResponse struct {
	Builds []*BuildResponse `json:"builds"`
	Total  int64            `json:"total"`
	Page   int              `json:"page"`
	Size   int              `json:"size"`
}

// DeploymentResponse 部署响应
type DeploymentResponse struct {
	*Deployment
	AppName      string `json:"app_name"`
	BuildNumber  int    `json:"build_number"`
}

// DeploymentListResponse 部署列表响应
type DeploymentListResponse struct {
	Deployments []*DeploymentResponse `json:"deployments"`
	Total       int64                 `json:"total"`
	Page        int                   `json:"page"`
	Size        int                   `json:"size"`
}

// WebhookPayload Webhook 载荷
type WebhookPayload struct {
	Source     string      `json:"source"`     // gitea, github, gitlab
	Event      string      `json:"event"`      // push, pull_request, tag
	Repository Repository  `json:"repository"`
	Commits    []Commit    `json:"commits"`
	Ref        string      `json:"ref"`        // refs/heads/main
	Before     string      `json:"before"`     // 之前的提交哈希
	After      string      `json:"after"`      // 当前的提交哈希
	Pusher     User        `json:"pusher"`
}

// Repository 仓库信息
type Repository struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	HTMLURL  string `json:"html_url"`
	CloneURL string `json:"clone_url"`
	SSHURL   string `json:"ssh_url"`
}

// Commit 提交信息
type Commit struct {
	ID        string    `json:"id"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	URL       string    `json:"url"`
	Author    User      `json:"author"`
	Committer User      `json:"committer"`
}

// User 用户信息
type User struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Name     string `json:"name"`
}

// BuildLogEntry 构建日志条目
type BuildLogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Stage     string `json:"stage"`
	Step      string `json:"step"`
	Message   string `json:"message"`
	BuildID   string `json:"build_id"`
	AppID     string `json:"app_id"`
}

// BuildStatistics 构建统计
type BuildStatistics struct {
	TotalBuilds   int64   `json:"total_builds"`
	SuccessBuilds int64   `json:"success_builds"`
	FailedBuilds  int64   `json:"failed_builds"`
	SuccessRate   float64 `json:"success_rate"`
	AvgDuration   float64 `json:"avg_duration"`   // 平均构建时长 (秒)
	TodayBuilds   int64   `json:"today_builds"`
}

// NodeStatistics 节点统计
type NodeStatistics struct {
	TotalNodes    int `json:"total_nodes"`
	OnlineNodes   int `json:"online_nodes"`
	OfflineNodes  int `json:"offline_nodes"`
	BusyNodes     int `json:"busy_nodes"`
	TotalCapacity int `json:"total_capacity"`
	UsedCapacity  int `json:"used_capacity"`
}

// Validate 验证流水线配置
func (c *PipelineConfig) Validate() error {
	if c.APIVersion == "" {
		c.APIVersion = "v1"
	}
	
	if c.Kind == "" {
		c.Kind = "Pipeline"
	}
	
	if c.Spec.Stages == nil || len(c.Spec.Stages) == 0 {
		return fmt.Errorf("流水线必须包含至少一个阶段")
	}
	
	// 验证阶段配置
	for i, stage := range c.Spec.Stages {
		if err := stage.Validate(); err != nil {
			return fmt.Errorf("阶段 %d 配置错误: %w", i+1, err)
		}
	}
	
	return nil
}

// Validate 验证阶段配置
func (s *Stage) Validate() error {
	if strings.TrimSpace(s.Name) == "" {
		return fmt.Errorf("阶段名称不能为空")
	}
	
	if s.Steps == nil || len(s.Steps) == 0 {
		return fmt.Errorf("阶段必须包含至少一个步骤")
	}
	
	// 验证步骤配置
	for i, step := range s.Steps {
		if err := step.Validate(); err != nil {
			return fmt.Errorf("步骤 %d 配置错误: %w", i+1, err)
		}
	}
	
	return nil
}

// Validate 验证步骤配置
func (s *Step) Validate() error {
	if strings.TrimSpace(s.Name) == "" {
		return fmt.Errorf("步骤名称不能为空")
	}
	
	if strings.TrimSpace(s.Type) == "" {
		return fmt.Errorf("步骤类型不能为空")
	}
	
	// 验证步骤类型
	validTypes := []string{"git_checkout", "shell", "docker_build", "docker_push", "deploy", "cache", "setup_python"}
	isValidType := false
	for _, validType := range validTypes {
		if s.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("无效的步骤类型: %s", s.Type)
	}
	
	return nil
}
