package cicd

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service CI/CD 服务接口
type Service interface {
	// 流水线管理
	CreatePipeline(ctx context.Context, req *CreatePipelineRequest) (*Pipeline, error)
	GetPipeline(ctx context.Context, pipelineID string) (*Pipeline, error)
	ListPipelines(ctx context.Context, appID string) ([]*Pipeline, error)
	UpdatePipeline(ctx context.Context, pipelineID string, req *UpdatePipelineRequest) (*Pipeline, error)
	DeletePipeline(ctx context.Context, pipelineID string) error
	
	// 构建管理
	CreateBuild(ctx context.Context, req *CreateBuildRequest) (*Build, error)
	GetBuild(ctx context.Context, buildID string) (*Build, error)
	ListBuilds(ctx context.Context, filter *BuildFilter) ([]*Build, int64, error)
	CancelBuild(ctx context.Context, buildID string) error
	RetryBuild(ctx context.Context, buildID string) (*Build, error)
	
	// 构建执行
	ExecuteBuild(ctx context.Context, buildID string) error
	UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error
	UpdateStageStatus(ctx context.Context, stageID string, status BuildStageStatus, log string) error
	UpdateStepStatus(ctx context.Context, stepID string, status BuildStepStatus, log string, exitCode int) error
	
	// 部署管理
	CreateDeployment(ctx context.Context, req *CreateDeploymentRequest) (*Deployment, error)
	GetDeployment(ctx context.Context, deploymentID string) (*Deployment, error)
	ListDeployments(ctx context.Context, filter *DeploymentFilter) ([]*Deployment, int64, error)
	RollbackDeployment(ctx context.Context, deploymentID string) error
	
	// 环境管理
	CreateEnvironment(ctx context.Context, req *CreateEnvironmentRequest) (*Environment, error)
	GetEnvironment(ctx context.Context, envID string) (*Environment, error)
	ListEnvironments(ctx context.Context, tenantID string) ([]*Environment, error)
	
	// Webhook 处理
	HandleWebhook(ctx context.Context, source string, payload interface{}) error
	
	// 构建节点管理
	RegisterBuildNode(ctx context.Context, req *RegisterNodeRequest) (*BuildNode, error)
	GetBuildNode(ctx context.Context, nodeID string) (*BuildNode, error)
	ListBuildNodes(ctx context.Context) ([]*BuildNode, error)
	UpdateNodeStatus(ctx context.Context, nodeID string, status NodeStatus) error
}

// CICDService CI/CD 服务实现
type CICDService struct {
	db     *gorm.DB
	logger Logger
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// NewCICDService 创建 CI/CD 服务
func NewCICDService(db *gorm.DB, logger Logger) Service {
	return &CICDService{
		db:     db,
		logger: logger,
	}
}

// CreatePipeline 创建流水线
func (s *CICDService) CreatePipeline(ctx context.Context, req *CreatePipelineRequest) (*Pipeline, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查应用是否存在流水线
	var existingPipeline Pipeline
	err := s.db.Where("app_id = ? AND name = ?", req.AppID, req.Name).First(&existingPipeline).Error
	if err == nil {
		return nil, fmt.Errorf("应用已存在名为 '%s' 的流水线", req.Name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查流水线失败: %w", err)
	}
	
	// 创建流水线
	pipeline := &Pipeline{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		AppID:       req.AppID,
		Config:      req.Config,
		Status:      PipelineStatusActive,
		TenantID:    req.TenantID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	if err := s.db.Create(pipeline).Error; err != nil {
		return nil, fmt.Errorf("创建流水线失败: %w", err)
	}
	
	s.logger.Info("流水线创建成功", "pipeline_id", pipeline.ID, "name", pipeline.Name, "app_id", req.AppID)
	return pipeline, nil
}

// GetPipeline 获取流水线详情
func (s *CICDService) GetPipeline(ctx context.Context, pipelineID string) (*Pipeline, error) {
	var pipeline Pipeline
	err := s.db.Preload("Builds").First(&pipeline, "id = ?", pipelineID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("流水线不存在: %s", pipelineID)
		}
		return nil, fmt.Errorf("获取流水线失败: %w", err)
	}
	return &pipeline, nil
}

// ListPipelines 获取流水线列表
func (s *CICDService) ListPipelines(ctx context.Context, appID string) ([]*Pipeline, error) {
	var pipelines []*Pipeline
	err := s.db.Where("app_id = ? AND status != ?", appID, PipelineStatusDeleted).
		Order("created_at DESC").Find(&pipelines).Error
	if err != nil {
		return nil, fmt.Errorf("获取流水线列表失败: %w", err)
	}
	return pipelines, nil
}

// CreateBuild 创建构建任务
func (s *CICDService) CreateBuild(ctx context.Context, req *CreateBuildRequest) (*Build, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 获取流水线信息
	pipeline, err := s.GetPipeline(ctx, req.PipelineID)
	if err != nil {
		return nil, err
	}
	
	// 生成构建编号
	var lastBuild Build
	s.db.Where("pipeline_id = ?", req.PipelineID).Order("number DESC").First(&lastBuild)
	buildNumber := lastBuild.Number + 1
	
	// 创建构建任务
	build := &Build{
		ID:          uuid.New().String(),
		PipelineID:  req.PipelineID,
		AppID:       pipeline.AppID,
		Number:      buildNumber,
		Branch:      req.Branch,
		CommitHash:  req.CommitHash,
		CommitMsg:   req.CommitMsg,
		Author:      req.Author,
		Status:      BuildStatusPending,
		TriggerType: req.TriggerType,
		TriggerBy:   req.TriggerBy,
		StartTime:   time.Now(),
	}
	
	if err := s.db.Create(build).Error; err != nil {
		return nil, fmt.Errorf("创建构建任务失败: %w", err)
	}
	
	// 创建构建阶段和步骤
	if err := s.createBuildStages(ctx, build, pipeline.Config.Spec.Stages); err != nil {
		s.logger.Error("创建构建阶段失败", "error", err, "build_id", build.ID)
	}
	
	// 将构建任务加入队列
	if err := s.enqueueBuild(ctx, build.ID, req.Priority); err != nil {
		s.logger.Error("构建任务入队失败", "error", err, "build_id", build.ID)
	}
	
	s.logger.Info("构建任务创建成功", "build_id", build.ID, "number", buildNumber, "app_id", pipeline.AppID)
	return build, nil
}

// GetBuild 获取构建详情
func (s *CICDService) GetBuild(ctx context.Context, buildID string) (*Build, error) {
	var build Build
	err := s.db.Preload("Pipeline").Preload("Stages.Steps").First(&build, "id = ?", buildID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("构建任务不存在: %s", buildID)
		}
		return nil, fmt.Errorf("获取构建任务失败: %w", err)
	}
	return &build, nil
}

// ListBuilds 获取构建列表
func (s *CICDService) ListBuilds(ctx context.Context, filter *BuildFilter) ([]*Build, int64, error) {
	query := s.db.Model(&Build{})
	
	// 应用过滤条件
	if filter != nil {
		if filter.AppID != "" {
			query = query.Where("app_id = ?", filter.AppID)
		}
		if filter.PipelineID != "" {
			query = query.Where("pipeline_id = ?", filter.PipelineID)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Branch != "" {
			query = query.Where("branch = ?", filter.Branch)
		}
		if filter.TriggerType != "" {
			query = query.Where("trigger_type = ?", filter.TriggerType)
		}
	}
	
	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取构建总数失败: %w", err)
	}
	
	// 分页查询
	var builds []*Build
	offset := 0
	limit := 20
	if filter != nil {
		if filter.Page > 0 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		if filter.PageSize > 0 && filter.PageSize <= 100 {
			limit = filter.PageSize
		}
	}
	
	err := query.Preload("Pipeline").Offset(offset).Limit(limit).
		Order("start_time DESC").Find(&builds).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取构建列表失败: %w", err)
	}
	
	return builds, total, nil
}

// CancelBuild 取消构建
func (s *CICDService) CancelBuild(ctx context.Context, buildID string) error {
	// 获取构建任务
	build, err := s.GetBuild(ctx, buildID)
	if err != nil {
		return err
	}
	
	// 检查构建状态
	if build.Status != BuildStatusPending && build.Status != BuildStatusRunning {
		return fmt.Errorf("构建任务状态为 %s，无法取消", build.Status)
	}
	
	// 更新构建状态
	endTime := time.Now()
	duration := int(endTime.Sub(build.StartTime).Seconds())
	
	err = s.db.Model(build).Updates(map[string]interface{}{
		"status":   BuildStatusCanceled,
		"end_time": &endTime,
		"duration": duration,
	}).Error
	if err != nil {
		return fmt.Errorf("取消构建失败: %w", err)
	}
	
	// 取消所有运行中的阶段和步骤
	s.db.Model(&BuildStage{}).Where("build_id = ? AND status IN ?", 
		buildID, []BuildStageStatus{BuildStageStatusPending, BuildStageStatusRunning}).
		Update("status", BuildStageStatusCanceled)
		
	s.db.Model(&BuildStep{}).Where("stage_id IN (SELECT id FROM build_stages WHERE build_id = ?) AND status IN ?",
		buildID, []BuildStepStatus{BuildStepStatusPending, BuildStepStatusRunning}).
		Update("status", BuildStepStatusCanceled)
	
	// 从构建队列中移除
	s.db.Model(&BuildQueue{}).Where("build_id = ?", buildID).Update("status", QueueStatusCanceled)
	
	s.logger.Info("构建任务已取消", "build_id", buildID)
	return nil
}

// createBuildStages 创建构建阶段
func (s *CICDService) createBuildStages(ctx context.Context, build *Build, stages []Stage) error {
	for _, stageConfig := range stages {
		stage := &BuildStage{
			ID:      uuid.New().String(),
			BuildID: build.ID,
			Name:    stageConfig.Name,
			Status:  BuildStageStatusPending,
		}
		
		if err := s.db.Create(stage).Error; err != nil {
			return fmt.Errorf("创建构建阶段失败: %w", err)
		}
		
		// 创建构建步骤
		for _, stepConfig := range stageConfig.Steps {
			step := &BuildStep{
				ID:      uuid.New().String(),
				StageID: stage.ID,
				Name:    stepConfig.Name,
				Type:    stepConfig.Type,
				Status:  BuildStepStatusPending,
			}
			
			if err := s.db.Create(step).Error; err != nil {
				return fmt.Errorf("创建构建步骤失败: %w", err)
			}
		}
	}
	
	return nil
}

// enqueueBuild 将构建任务加入队列
func (s *CICDService) enqueueBuild(ctx context.Context, buildID string, priority int) error {
	queue := &BuildQueue{
		ID:        uuid.New().String(),
		BuildID:   buildID,
		Priority:  priority,
		Status:    QueueStatusWaiting,
		CreatedAt: time.Now(),
	}
	
	if err := s.db.Create(queue).Error; err != nil {
		return fmt.Errorf("构建任务入队失败: %w", err)
	}
	
	s.logger.Info("构建任务已入队", "build_id", buildID, "priority", priority)
	return nil
}

// ExecuteBuild 执行构建
func (s *CICDService) ExecuteBuild(ctx context.Context, buildID string) error {
	// 获取构建任务
	build, err := s.GetBuild(ctx, buildID)
	if err != nil {
		return err
	}
	
	// 更新构建状态为运行中
	if err := s.UpdateBuildStatus(ctx, buildID, BuildStatusRunning, "开始执行构建..."); err != nil {
		return err
	}
	
	// 执行构建阶段
	for _, stage := range build.Stages {
		if err := s.executeStage(ctx, &stage, build); err != nil {
			s.logger.Error("执行构建阶段失败", "error", err, "stage_id", stage.ID, "build_id", buildID)
			s.UpdateBuildStatus(ctx, buildID, BuildStatusFailed, fmt.Sprintf("阶段 '%s' 执行失败: %v", stage.Name, err))
			return err
		}
	}
	
	// 构建成功
	s.UpdateBuildStatus(ctx, buildID, BuildStatusSuccess, "构建完成")
	s.logger.Info("构建执行完成", "build_id", buildID)
	return nil
}

// executeStage 执行构建阶段
func (s *CICDService) executeStage(ctx context.Context, stage *BuildStage, build *Build) error {
	// 更新阶段状态为运行中
	startTime := time.Now()
	s.UpdateStageStatus(ctx, stage.ID, BuildStageStatusRunning, "开始执行阶段...")
	s.db.Model(stage).Update("start_time", &startTime)
	
	// 执行阶段中的步骤
	for _, step := range stage.Steps {
		if err := s.executeStep(ctx, &step, stage, build); err != nil {
			// 阶段失败
			endTime := time.Now()
			duration := int(endTime.Sub(startTime).Seconds())
			s.db.Model(stage).Updates(map[string]interface{}{
				"status":   BuildStageStatusFailed,
				"end_time": &endTime,
				"duration": duration,
				"error_msg": err.Error(),
			})
			return err
		}
	}
	
	// 阶段成功
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	s.db.Model(stage).Updates(map[string]interface{}{
		"status":   BuildStageStatusSuccess,
		"end_time": &endTime,
		"duration": duration,
	})
	
	s.logger.Info("构建阶段完成", "stage_id", stage.ID, "name", stage.Name)
	return nil
}

// executeStep 执行构建步骤
func (s *CICDService) executeStep(ctx context.Context, step *BuildStep, stage *BuildStage, build *Build) error {
	// 更新步骤状态为运行中
	startTime := time.Now()
	s.UpdateStepStatus(ctx, step.ID, BuildStepStatusRunning, "开始执行步骤...", 0)
	s.db.Model(step).Update("start_time", &startTime)
	
	// TODO: 根据步骤类型执行具体逻辑
	// 这里先模拟执行过程
	time.Sleep(5 * time.Second)
	
	// 步骤成功
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Seconds())
	s.db.Model(step).Updates(map[string]interface{}{
		"status":    BuildStepStatusSuccess,
		"end_time":  &endTime,
		"duration":  duration,
		"exit_code": 0,
	})
	
	s.logger.Info("构建步骤完成", "step_id", step.ID, "name", step.Name)
	return nil
}

// UpdateBuildStatus 更新构建状态
func (s *CICDService) UpdateBuildStatus(ctx context.Context, buildID string, status BuildStatus, log string) error {
	updates := map[string]interface{}{
		"status":    status,
		"build_log": log,
	}
	
	// 如果构建完成，设置结束时间
	if status == BuildStatusSuccess || status == BuildStatusFailed || status == BuildStatusCanceled || status == BuildStatusTimeout {
		endTime := time.Now()
		updates["end_time"] = &endTime
		
		// 计算构建时长
		var build Build
		if err := s.db.First(&build, "id = ?", buildID).Error; err == nil {
			duration := int(endTime.Sub(build.StartTime).Seconds())
			updates["duration"] = duration
		}
	}
	
	err := s.db.Model(&Build{}).Where("id = ?", buildID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新构建状态失败: %w", err)
	}
	
	s.logger.Info("构建状态更新", "build_id", buildID, "status", status)
	return nil
}

// UpdateStageStatus 更新阶段状态
func (s *CICDService) UpdateStageStatus(ctx context.Context, stageID string, status BuildStageStatus, log string) error {
	err := s.db.Model(&BuildStage{}).Where("id = ?", stageID).Updates(map[string]interface{}{
		"status": status,
		"log":    log,
	}).Error
	if err != nil {
		return fmt.Errorf("更新阶段状态失败: %w", err)
	}
	
	s.logger.Debug("阶段状态更新", "stage_id", stageID, "status", status)
	return nil
}

// UpdateStepStatus 更新步骤状态
func (s *CICDService) UpdateStepStatus(ctx context.Context, stepID string, status BuildStepStatus, log string, exitCode int) error {
	err := s.db.Model(&BuildStep{}).Where("id = ?", stepID).Updates(map[string]interface{}{
		"status":    status,
		"log":       log,
		"exit_code": exitCode,
	}).Error
	if err != nil {
		return fmt.Errorf("更新步骤状态失败: %w", err)
	}
	
	s.logger.Debug("步骤状态更新", "step_id", stepID, "status", status, "exit_code", exitCode)
	return nil
}
