package cicd

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Handler CI/CD HTTP 处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建 CI/CD 处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 流水线管理
	pipelines := router.Group("/pipelines")
	{
		pipelines.POST("", h.CreatePipeline)           // 创建流水线
		pipelines.GET("", h.ListPipelines)             // 获取流水线列表
		pipelines.GET("/:id", h.GetPipeline)           // 获取流水线详情
		pipelines.PUT("/:id", h.UpdatePipeline)        // 更新流水线
		pipelines.DELETE("/:id", h.DeletePipeline)     // 删除流水线
	}
	
	// 构建管理
	builds := router.Group("/builds")
	{
		builds.POST("", h.CreateBuild)                 // 创建构建
		builds.GET("", h.ListBuilds)                   // 获取构建列表
		builds.GET("/:id", h.GetBuild)                 // 获取构建详情
		builds.POST("/:id/cancel", h.CancelBuild)      // 取消构建
		builds.POST("/:id/retry", h.RetryBuild)        // 重试构建
		builds.GET("/:id/logs", h.GetBuildLogs)        // 获取构建日志
		builds.GET("/:id/artifacts", h.GetArtifacts)   // 获取构建产物
	}
	
	// 部署管理
	deployments := router.Group("/deployments")
	{
		deployments.POST("", h.CreateDeployment)           // 创建部署
		deployments.GET("", h.ListDeployments)             // 获取部署列表
		deployments.GET("/:id", h.GetDeployment)           // 获取部署详情
		deployments.POST("/:id/rollback", h.RollbackDeployment) // 回滚部署
	}
	
	// 环境管理
	environments := router.Group("/environments")
	{
		environments.POST("", h.CreateEnvironment)     // 创建环境
		environments.GET("", h.ListEnvironments)       // 获取环境列表
		environments.GET("/:id", h.GetEnvironment)     // 获取环境详情
	}
	
	// Webhook 处理
	webhooks := router.Group("/webhooks")
	{
		webhooks.POST("/gitea", h.HandleGiteaWebhook)   // Gitea Webhook
		webhooks.POST("/github", h.HandleGithubWebhook) // GitHub Webhook
		webhooks.POST("/gitlab", h.HandleGitlabWebhook) // GitLab Webhook
	}
	
	// 构建节点管理
	nodes := router.Group("/nodes")
	{
		nodes.POST("", h.RegisterBuildNode)         // 注册构建节点
		nodes.GET("", h.ListBuildNodes)             // 获取构建节点列表
		nodes.GET("/:id", h.GetBuildNode)           // 获取构建节点详情
		nodes.PUT("/:id/status", h.UpdateNodeStatus) // 更新节点状态
	}
	
	// 统计信息
	stats := router.Group("/stats")
	{
		stats.GET("/builds", h.GetBuildStatistics)  // 获取构建统计
		stats.GET("/nodes", h.GetNodeStatistics)   // 获取节点统计
	}
}

// CreatePipeline 创建流水线
// @Summary 创建流水线
// @Description 为应用创建 CI/CD 流水线
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreatePipelineRequest true "创建流水线请求"
// @Success 201 {object} Pipeline "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines [post]
func (h *Handler) CreatePipeline(c *gin.Context) {
	var req CreatePipelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	// 从上下文获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "未授权访问",
		})
		return
	}
	req.TenantID = tenantID.(string)
	
	pipeline, err := h.service.CreatePipeline(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建流水线失败", "error", err, "name", req.Name)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_PIPELINE_FAILED",
			Message: "创建流水线失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("流水线创建成功", "pipeline_id", pipeline.ID, "name", pipeline.Name)
	c.JSON(http.StatusCreated, pipeline)
}

// GetPipeline 获取流水线详情
// @Summary 获取流水线详情
// @Description 根据流水线ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "流水线ID"
// @Success 200 {object} Pipeline "获取成功"
// @Failure 404 {object} ErrorResponse "流水线不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines/{id} [get]
func (h *Handler) GetPipeline(c *gin.Context) {
	pipelineID := c.Param("id")
	if pipelineID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PIPELINE_ID",
			Message: "流水线ID不能为空",
		})
		return
	}
	
	pipeline, err := h.service.GetPipeline(c.Request.Context(), pipelineID)
	if err != nil {
		h.logger.Error("获取流水线失败", "error", err, "pipeline_id", pipelineID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "PIPELINE_NOT_FOUND",
			Message: "流水线不存在",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, pipeline)
}

// ListPipelines 获取流水线列表
// @Summary 获取流水线列表
// @Description 获取指定应用的流水线列表
// @Tags CI/CD
// @Produce json
// @Param app_id query string true "应用ID"
// @Success 200 {array} PipelineResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/pipelines [get]
func (h *Handler) ListPipelines(c *gin.Context) {
	appID := c.Query("app_id")
	if appID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "MISSING_APP_ID",
			Message: "缺少应用ID参数",
		})
		return
	}
	
	pipelines, err := h.service.ListPipelines(c.Request.Context(), appID)
	if err != nil {
		h.logger.Error("获取流水线列表失败", "error", err, "app_id", appID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_PIPELINES_FAILED",
			Message: "获取流水线列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	responses := make([]*PipelineResponse, len(pipelines))
	for i, pipeline := range pipelines {
		responses[i] = &PipelineResponse{
			Pipeline:      pipeline,
			BuildCount:    len(pipeline.Builds),
			SuccessCount:  countSuccessBuilds(pipeline.Builds),
			FailureCount:  countFailedBuilds(pipeline.Builds),
			LastBuildTime: getLastBuildTime(pipeline.Builds),
		}
	}
	
	c.JSON(http.StatusOK, responses)
}

// CreateBuild 创建构建
// @Summary 创建构建
// @Description 创建新的构建任务
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param request body CreateBuildRequest true "创建构建请求"
// @Success 201 {object} Build "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds [post]
func (h *Handler) CreateBuild(c *gin.Context) {
	var req CreateBuildRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}
	
	build, err := h.service.CreateBuild(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建构建失败", "error", err, "pipeline_id", req.PipelineID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_BUILD_FAILED",
			Message: "创建构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建创建成功", "build_id", build.ID, "number", build.Number)
	c.JSON(http.StatusCreated, build)
}

// ListBuilds 获取构建列表
// @Summary 获取构建列表
// @Description 获取构建任务列表
// @Tags CI/CD
// @Produce json
// @Param app_id query string false "应用ID"
// @Param pipeline_id query string false "流水线ID"
// @Param status query string false "状态过滤"
// @Param branch query string false "分支过滤"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} BuildListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds [get]
func (h *Handler) ListBuilds(c *gin.Context) {
	// 解析查询参数
	filter := &BuildFilter{
		AppID:       c.Query("app_id"),
		PipelineID:  c.Query("pipeline_id"),
		Status:      c.Query("status"),
		Branch:      c.Query("branch"),
		TriggerType: c.Query("trigger_type"),
		Page:        1,
		PageSize:    20,
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}
	
	builds, total, err := h.service.ListBuilds(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取构建列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_BUILDS_FAILED",
			Message: "获取构建列表失败",
			Details: err.Error(),
		})
		return
	}
	
	// 转换为响应格式
	buildResponses := make([]*BuildResponse, len(builds))
	for i, build := range builds {
		buildResponses[i] = &BuildResponse{
			Build:        build,
			PipelineName: build.Pipeline.Name,
			AppName:      "", // TODO: 从应用服务获取应用名称
		}
	}
	
	response := &BuildListResponse{
		Builds: buildResponses,
		Total:  total,
		Page:   filter.Page,
		Size:   len(buildResponses),
	}
	
	c.JSON(http.StatusOK, response)
}

// GetBuild 获取构建详情
// @Summary 获取构建详情
// @Description 根据构建ID获取详细信息
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} Build "获取成功"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id} [get]
func (h *Handler) GetBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	build, err := h.service.GetBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("获取构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "BUILD_NOT_FOUND",
			Message: "构建任务不存在",
			Details: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, build)
}

// CancelBuild 取消构建
// @Summary 取消构建
// @Description 取消正在进行的构建任务
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} object "取消成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/cancel [post]
func (h *Handler) CancelBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	err := h.service.CancelBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("取消构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CANCEL_BUILD_FAILED",
			Message: "取消构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建已取消", "build_id", buildID)
	c.JSON(http.StatusOK, gin.H{"message": "构建已取消"})
}

// RetryBuild 重试构建
// @Summary 重试构建
// @Description 重试失败的构建任务
// @Tags CI/CD
// @Produce json
// @Param id path string true "构建ID"
// @Success 200 {object} Build "重试成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "构建不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/builds/{id}/retry [post]
func (h *Handler) RetryBuild(c *gin.Context) {
	buildID := c.Param("id")
	if buildID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_BUILD_ID",
			Message: "构建ID不能为空",
		})
		return
	}
	
	newBuild, err := h.service.RetryBuild(c.Request.Context(), buildID)
	if err != nil {
		h.logger.Error("重试构建失败", "error", err, "build_id", buildID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "RETRY_BUILD_FAILED",
			Message: "重试构建失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("构建重试成功", "old_build_id", buildID, "new_build_id", newBuild.ID)
	c.JSON(http.StatusOK, newBuild)
}

// HandleGiteaWebhook 处理 Gitea Webhook
// @Summary 处理 Gitea Webhook
// @Description 处理来自 Gitea 的 Webhook 事件
// @Tags CI/CD
// @Accept json
// @Produce json
// @Param payload body object true "Webhook 载荷"
// @Success 200 {object} object "处理成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/webhooks/gitea [post]
func (h *Handler) HandleGiteaWebhook(c *gin.Context) {
	var payload interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		h.logger.Error("绑定 Webhook 载荷失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_WEBHOOK_PAYLOAD",
			Message: "Webhook 载荷格式错误",
			Details: err.Error(),
		})
		return
	}
	
	err := h.service.HandleWebhook(c.Request.Context(), "gitea", payload)
	if err != nil {
		h.logger.Error("处理 Gitea Webhook 失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "WEBHOOK_PROCESSING_FAILED",
			Message: "Webhook 处理失败",
			Details: err.Error(),
		})
		return
	}
	
	h.logger.Info("Gitea Webhook 处理成功")
	c.JSON(http.StatusOK, gin.H{"message": "Webhook 处理成功"})
}

// HandleGithubWebhook 处理 GitHub Webhook
func (h *Handler) HandleGithubWebhook(c *gin.Context) {
	// TODO: 实现 GitHub Webhook 处理逻辑
	c.JSON(http.StatusOK, gin.H{"message": "GitHub Webhook 处理功能开发中"})
}

// HandleGitlabWebhook 处理 GitLab Webhook
func (h *Handler) HandleGitlabWebhook(c *gin.Context) {
	// TODO: 实现 GitLab Webhook 处理逻辑
	c.JSON(http.StatusOK, gin.H{"message": "GitLab Webhook 处理功能开发中"})
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Details   string `json:"details,omitempty"`
	Timestamp string `json:"timestamp"`
}

// 辅助函数
func countSuccessBuilds(builds []Build) int {
	count := 0
	for _, build := range builds {
		if build.Status == BuildStatusSuccess {
			count++
		}
	}
	return count
}

func countFailedBuilds(builds []Build) int {
	count := 0
	for _, build := range builds {
		if build.Status == BuildStatusFailed {
			count++
		}
	}
	return count
}

func getLastBuildTime(builds []Build) *time.Time {
	if len(builds) == 0 {
		return nil
	}
	
	latest := builds[0].StartTime
	for _, build := range builds {
		if build.StartTime.After(latest) {
			latest = build.StartTime
		}
	}
	
	return &latest
}
