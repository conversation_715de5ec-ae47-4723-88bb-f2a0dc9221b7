package config

import (
	"time"
)

// Config 配置实体
type Config struct {
	ID          string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Key         string         `json:"key" gorm:"type:varchar(200);not null;index"`
	Value       interface{}    `json:"value" gorm:"type:jsonb"`
	Type        ConfigType     `json:"type" gorm:"type:varchar(20);not null"`
	Scope       ConfigScope    `json:"scope" gorm:"type:varchar(20);not null;index"`
	ScopeID     string         `json:"scope_id" gorm:"type:varchar(36);index"`
	Environment string         `json:"environment" gorm:"type:varchar(50);not null;index"`
	Encrypted   bool           `json:"encrypted" gorm:"default:false"`
	Description string         `json:"description" gorm:"type:text"`
	Tags        []string       `json:"tags" gorm:"type:jsonb"`
	Metadata    ConfigMetadata `json:"metadata" gorm:"type:jsonb"`
	Schema      interface{}    `json:"schema" gorm:"type:jsonb"`      // JSON Schema for validation
	Version     int            `json:"version" gorm:"not null;default:1"`
	Status      ConfigStatus   `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy   string         `json:"created_by" gorm:"type:varchar(36);not null"`
	UpdatedBy   string         `json:"updated_by" gorm:"type:varchar(36)"`
	
	// 关联关系
	Versions []ConfigVersion `json:"versions,omitempty" gorm:"foreignKey:ConfigID"`
}

// ConfigType 配置类型
type ConfigType string

const (
	ConfigTypeString ConfigType = "string"
	ConfigTypeNumber ConfigType = "number"
	ConfigTypeBool   ConfigType = "bool"
	ConfigTypeJSON   ConfigType = "json"
	ConfigTypeYAML   ConfigType = "yaml"
	ConfigTypeSecret ConfigType = "secret"
	ConfigTypeFile   ConfigType = "file"
	ConfigTypeList   ConfigType = "list"
	ConfigTypeMap    ConfigType = "map"
)

// ConfigScope 配置作用域
type ConfigScope string

const (
	ConfigScopeGlobal      ConfigScope = "global"
	ConfigScopePlatform    ConfigScope = "platform"
	ConfigScopeTenant      ConfigScope = "tenant"
	ConfigScopeApplication ConfigScope = "application"
	ConfigScopeService     ConfigScope = "service"
	ConfigScopeUser        ConfigScope = "user"
)

// ConfigStatus 配置状态
type ConfigStatus string

const (
	ConfigStatusActive   ConfigStatus = "active"   // 活跃
	ConfigStatusInactive ConfigStatus = "inactive" // 非活跃
	ConfigStatusDeleted  ConfigStatus = "deleted"  // 已删除
)

// ConfigMetadata 配置元数据
type ConfigMetadata struct {
	Source      string            `json:"source"`       // 配置来源
	Owner       string            `json:"owner"`        // 配置所有者
	Category    string            `json:"category"`     // 配置分类
	Priority    int               `json:"priority"`     // 优先级
	Sensitive   bool              `json:"sensitive"`    // 是否敏感
	ReadOnly    bool              `json:"read_only"`    // 是否只读
	Required    bool              `json:"required"`     // 是否必需
	Deprecated  bool              `json:"deprecated"`   // 是否已废弃
	Labels      map[string]string `json:"labels"`       // 标签
	Annotations map[string]string `json:"annotations"`  // 注解
}

// ConfigVersion 配置版本
type ConfigVersion struct {
	ID           string     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	ConfigID     string     `json:"config_id" gorm:"type:varchar(36);not null;index"`
	Version      int        `json:"version" gorm:"not null"`
	Value        interface{} `json:"value" gorm:"type:jsonb"`
	ChangeType   ChangeType `json:"change_type" gorm:"type:varchar(20);not null"`
	ChangeReason string     `json:"change_reason" gorm:"type:text"`
	ChangedBy    string     `json:"changed_by" gorm:"type:varchar(36);not null"`
	CreatedAt    time.Time  `json:"created_at" gorm:"autoCreateTime"`
	
	// 关联关系
	Config Config `json:"config,omitempty" gorm:"foreignKey:ConfigID"`
}

// ChangeType 变更类型
type ChangeType string

const (
	ChangeTypeCreate ChangeType = "create"
	ChangeTypeUpdate ChangeType = "update"
	ChangeTypeDelete ChangeType = "delete"
)

// ConfigTemplate 配置模板
type ConfigTemplate struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string                 `json:"name" gorm:"type:varchar(100);not null;uniqueIndex"`
	Description string                 `json:"description" gorm:"type:text"`
	Category    string                 `json:"category" gorm:"type:varchar(50);not null;index"`
	Template    string                 `json:"template" gorm:"type:text;not null"`
	Variables   []TemplateVariable     `json:"variables" gorm:"type:jsonb"`
	Schema      interface{}            `json:"schema" gorm:"type:jsonb"`
	Tags        []string               `json:"tags" gorm:"type:jsonb"`
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	Version     string                 `json:"version" gorm:"type:varchar(20);not null"`
	Status      ConfigStatus           `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time              `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy   string                 `json:"created_by" gorm:"type:varchar(36);not null"`
	UpdatedBy   string                 `json:"updated_by" gorm:"type:varchar(36)"`
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`         // string, number, bool, list, map
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"default_value"`
	Required     bool        `json:"required"`
	Validation   Validation  `json:"validation"`
}

// Validation 验证规则
type Validation struct {
	Pattern   string        `json:"pattern"`    // 正则表达式
	MinLength int           `json:"min_length"` // 最小长度
	MaxLength int           `json:"max_length"` // 最大长度
	Min       float64       `json:"min"`        // 最小值
	Max       float64       `json:"max"`        // 最大值
	Enum      []interface{} `json:"enum"`       // 枚举值
}

// Secret 密钥实体
type Secret struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string            `json:"name" gorm:"type:varchar(100);not null"`
	Key         string            `json:"key" gorm:"type:varchar(200);not null;index"`
	Value       string            `json:"-" gorm:"type:text;not null"`                    // 加密存储
	Type        SecretType        `json:"type" gorm:"type:varchar(20);not null"`
	Scope       ConfigScope       `json:"scope" gorm:"type:varchar(20);not null;index"`
	ScopeID     string            `json:"scope_id" gorm:"type:varchar(36);index"`
	Environment string            `json:"environment" gorm:"type:varchar(50);not null;index"`
	Description string            `json:"description" gorm:"type:text"`
	Tags        []string          `json:"tags" gorm:"type:jsonb"`
	Metadata    map[string]string `json:"metadata" gorm:"type:jsonb"`
	ExpiresAt   *time.Time        `json:"expires_at" gorm:"index"`
	RotationPolicy *RotationPolicy `json:"rotation_policy" gorm:"type:jsonb"`
	LastRotated *time.Time        `json:"last_rotated"`
	Version     int               `json:"version" gorm:"not null;default:1"`
	Status      ConfigStatus      `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy   string            `json:"created_by" gorm:"type:varchar(36);not null"`
	UpdatedBy   string            `json:"updated_by" gorm:"type:varchar(36)"`
	
	// 关联关系
	Versions []SecretVersion `json:"versions,omitempty" gorm:"foreignKey:SecretID"`
}

// SecretType 密钥类型
type SecretType string

const (
	SecretTypeGeneric    SecretType = "generic"     // 通用密钥
	SecretTypePassword   SecretType = "password"    // 密码
	SecretTypeAPIKey     SecretType = "api_key"     // API 密钥
	SecretTypeCertificate SecretType = "certificate" // 证书
	SecretTypeSSHKey     SecretType = "ssh_key"     // SSH 密钥
	SecretTypeToken      SecretType = "token"       // 令牌
	SecretTypeDatabase   SecretType = "database"    // 数据库连接
)

// RotationPolicy 轮换策略
type RotationPolicy struct {
	Enabled        bool          `json:"enabled"`
	Interval       time.Duration `json:"interval"`        // 轮换间隔
	MaxAge         time.Duration `json:"max_age"`         // 最大年龄
	NotifyBefore   time.Duration `json:"notify_before"`   // 提前通知时间
	AutoRotate     bool          `json:"auto_rotate"`     // 是否自动轮换
	BackupVersions int           `json:"backup_versions"` // 备份版本数
}

// SecretVersion 密钥版本
type SecretVersion struct {
	ID        string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	SecretID  string    `json:"secret_id" gorm:"type:varchar(36);not null;index"`
	Version   int       `json:"version" gorm:"not null"`
	Value     string    `json:"-" gorm:"type:text;not null"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	CreatedBy string    `json:"created_by" gorm:"type:varchar(36);not null"`
	
	// 关联关系
	Secret Secret `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
}

// ConfigSet 配置集
type ConfigSet struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string            `json:"name" gorm:"type:varchar(100);not null"`
	Description string            `json:"description" gorm:"type:text"`
	Scope       ConfigScope       `json:"scope" gorm:"type:varchar(20);not null;index"`
	ScopeID     string            `json:"scope_id" gorm:"type:varchar(36);index"`
	Environment string            `json:"environment" gorm:"type:varchar(50);not null;index"`
	Configs     []string          `json:"configs" gorm:"type:jsonb"`              // 配置ID列表
	Tags        []string          `json:"tags" gorm:"type:jsonb"`
	Metadata    map[string]string `json:"metadata" gorm:"type:jsonb"`
	Version     int               `json:"version" gorm:"not null;default:1"`
	Status      ConfigStatus      `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy   string            `json:"created_by" gorm:"type:varchar(36);not null"`
	UpdatedBy   string            `json:"updated_by" gorm:"type:varchar(36)"`
}

// ConfigSnapshot 配置快照
type ConfigSnapshot struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string            `json:"name" gorm:"type:varchar(100);not null"`
	Description string            `json:"description" gorm:"type:text"`
	Scope       ConfigScope       `json:"scope" gorm:"type:varchar(20);not null;index"`
	ScopeID     string            `json:"scope_id" gorm:"type:varchar(36);index"`
	Environment string            `json:"environment" gorm:"type:varchar(50);not null;index"`
	Data        interface{}       `json:"data" gorm:"type:jsonb"`                 // 快照数据
	Checksum    string            `json:"checksum" gorm:"type:varchar(64)"`       // 数据校验和
	Tags        []string          `json:"tags" gorm:"type:jsonb"`
	Metadata    map[string]string `json:"metadata" gorm:"type:jsonb"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	CreatedBy   string            `json:"created_by" gorm:"type:varchar(36);not null"`
}

// ConfigWatch 配置监听
type ConfigWatch struct {
	ID          string      `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID      string      `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Key         string      `json:"key" gorm:"type:varchar(200);not null;index"`
	Scope       ConfigScope `json:"scope" gorm:"type:varchar(20);not null"`
	ScopeID     string      `json:"scope_id" gorm:"type:varchar(36)"`
	Environment string      `json:"environment" gorm:"type:varchar(50);not null"`
	WebhookURL  string      `json:"webhook_url" gorm:"type:varchar(500)"`
	Events      []string    `json:"events" gorm:"type:jsonb"`               // 监听的事件类型
	Filters     interface{} `json:"filters" gorm:"type:jsonb"`              // 过滤条件
	Status      string      `json:"status" gorm:"type:varchar(20);not null;default:active"`
	CreatedAt   time.Time   `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time   `json:"updated_at" gorm:"autoUpdateTime"`
}

// ConfigAuditLog 配置审计日志
type ConfigAuditLog struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:varchar(36)"`
	ConfigID    string                 `json:"config_id" gorm:"type:varchar(36);index"`
	Action      string                 `json:"action" gorm:"type:varchar(50);not null;index"`
	UserID      string                 `json:"user_id" gorm:"type:varchar(36);not null;index"`
	Username    string                 `json:"username" gorm:"type:varchar(50)"`
	IPAddress   string                 `json:"ip_address" gorm:"type:varchar(45)"`
	UserAgent   string                 `json:"user_agent" gorm:"type:varchar(500)"`
	OldValue    interface{}            `json:"old_value" gorm:"type:jsonb"`
	NewValue    interface{}            `json:"new_value" gorm:"type:jsonb"`
	Changes     map[string]interface{} `json:"changes" gorm:"type:jsonb"`
	Reason      string                 `json:"reason" gorm:"type:text"`
	Success     bool                   `json:"success" gorm:"not null;index"`
	ErrorMsg    string                 `json:"error_msg" gorm:"type:text"`
	Timestamp   time.Time              `json:"timestamp" gorm:"autoCreateTime;index"`
}

// ConfigDistribution 配置分发记录
type ConfigDistribution struct {
	ID          string               `json:"id" gorm:"primaryKey;type:varchar(36)"`
	ConfigID    string               `json:"config_id" gorm:"type:varchar(36);not null;index"`
	TargetType  string               `json:"target_type" gorm:"type:varchar(20);not null"`   // instance, cluster, service
	TargetID    string               `json:"target_id" gorm:"type:varchar(100);not null;index"`
	Version     int                  `json:"version" gorm:"not null"`
	Status      DistributionStatus   `json:"status" gorm:"type:varchar(20);not null;index"`
	StartTime   time.Time            `json:"start_time" gorm:"autoCreateTime"`
	EndTime     *time.Time           `json:"end_time"`
	Duration    int                  `json:"duration"`                                       // 分发时长 (秒)
	ErrorMsg    string               `json:"error_msg" gorm:"type:text"`
	Retries     int                  `json:"retries" gorm:"default:0"`
	MaxRetries  int                  `json:"max_retries" gorm:"default:3"`
	NextRetry   *time.Time           `json:"next_retry"`
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	
	// 关联关系
	Config Config `json:"config,omitempty" gorm:"foreignKey:ConfigID"`
}

// DistributionStatus 分发状态
type DistributionStatus string

const (
	DistributionStatusPending  DistributionStatus = "pending"  // 等待中
	DistributionStatusRunning  DistributionStatus = "running"  // 分发中
	DistributionStatusSuccess  DistributionStatus = "success"  // 成功
	DistributionStatusFailed   DistributionStatus = "failed"   // 失败
	DistributionStatusTimeout  DistributionStatus = "timeout"  // 超时
	DistributionStatusCanceled DistributionStatus = "canceled" // 已取消
)

// FeatureFlag 功能开关
type FeatureFlag struct {
	ID          string            `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Key         string            `json:"key" gorm:"type:varchar(200);not null;uniqueIndex"`
	Name        string            `json:"name" gorm:"type:varchar(100);not null"`
	Description string            `json:"description" gorm:"type:text"`
	Type        FeatureFlagType   `json:"type" gorm:"type:varchar(20);not null"`
	Enabled     bool              `json:"enabled" gorm:"not null;default:false"`
	Rules       []FeatureFlagRule `json:"rules" gorm:"type:jsonb"`
	Rollout     *RolloutConfig    `json:"rollout" gorm:"type:jsonb"`
	Tags        []string          `json:"tags" gorm:"type:jsonb"`
	Metadata    map[string]string `json:"metadata" gorm:"type:jsonb"`
	CreatedAt   time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time         `json:"updated_at" gorm:"autoUpdateTime"`
	CreatedBy   string            `json:"created_by" gorm:"type:varchar(36);not null"`
	UpdatedBy   string            `json:"updated_by" gorm:"type:varchar(36)"`
}

// FeatureFlagType 功能开关类型
type FeatureFlagType string

const (
	FeatureFlagTypeBool    FeatureFlagType = "bool"    // 布尔开关
	FeatureFlagTypeString  FeatureFlagType = "string"  // 字符串开关
	FeatureFlagTypeNumber  FeatureFlagType = "number"  // 数字开关
	FeatureFlagTypeJSON    FeatureFlagType = "json"    // JSON 开关
)

// FeatureFlagRule 功能开关规则
type FeatureFlagRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Conditions  []FeatureFlagCondition `json:"conditions"`
	Value       interface{}            `json:"value"`
	Enabled     bool                   `json:"enabled"`
	Priority    int                    `json:"priority"`
}

// FeatureFlagCondition 功能开关条件
type FeatureFlagCondition struct {
	Attribute string      `json:"attribute"` // user_id, tenant_id, environment, etc.
	Operator  string      `json:"operator"`  // eq, ne, in, not_in, contains, etc.
	Value     interface{} `json:"value"`
}

// RolloutConfig 灰度发布配置
type RolloutConfig struct {
	Strategy   string  `json:"strategy"`   // percentage, user_list, tenant_list
	Percentage float64 `json:"percentage"` // 灰度百分比
	UserList   []string `json:"user_list"` // 用户白名单
	TenantList []string `json:"tenant_list"` // 租户白名单
}

// TableName 指定表名
func (Config) TableName() string {
	return "configs"
}

func (ConfigVersion) TableName() string {
	return "config_versions"
}

func (ConfigTemplate) TableName() string {
	return "config_templates"
}

func (Secret) TableName() string {
	return "secrets"
}

func (SecretVersion) TableName() string {
	return "secret_versions"
}

func (ConfigSet) TableName() string {
	return "config_sets"
}

func (ConfigSnapshot) TableName() string {
	return "config_snapshots"
}

func (ConfigWatch) TableName() string {
	return "config_watches"
}

func (ConfigAuditLog) TableName() string {
	return "config_audit_logs"
}

func (ConfigDistribution) TableName() string {
	return "config_distributions"
}

func (FeatureFlag) TableName() string {
	return "feature_flags"
}
