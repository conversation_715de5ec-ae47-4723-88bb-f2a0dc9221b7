package script

import (
	"net/http"
	"strconv"
	"time"

	"paas-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Handler 脚本执行HTTP处理器
type Handler struct {
	service ScriptExecutionService
	logger  logger.Logger
}

// NewHandler 创建脚本执行处理器
func NewHandler(service ScriptExecutionService, logger logger.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 脚本执行相关路由
	scripts := router.Group("/scripts")
	{
		scripts.POST("/execute", h.ExecuteScript)           // 执行脚本
		scripts.GET("/tasks", h.ListTasks)                  // 获取任务列表
		scripts.GET("/tasks/:id", h.GetTaskStatus)          // 获取任务状态
		scripts.GET("/tasks/:id/result", h.GetTaskResult)   // 获取任务结果
		scripts.GET("/tasks/:id/logs", h.GetTaskLogs)       // 获取任务日志
		scripts.DELETE("/tasks/:id", h.CancelTask)          // 取消任务
		scripts.GET("/tasks/:id/artifacts/:name", h.DownloadArtifact) // 下载产物
	}

	// 脚本模板相关路由
	templates := router.Group("/script-templates")
	{
		templates.POST("", h.CreateTemplate)                // 创建模板
		templates.GET("", h.ListTemplates)                  // 获取模板列表
		templates.GET("/:id", h.GetTemplate)                // 获取模板详情
		templates.PUT("/:id", h.UpdateTemplate)             // 更新模板
		templates.DELETE("/:id", h.DeleteTemplate)          // 删除模板
	}

	// 任务调度相关路由
	schedules := router.Group("/script-schedules")
	{
		schedules.POST("", h.CreateSchedule)                // 创建调度
		schedules.GET("", h.ListSchedules)                  // 获取调度列表
		schedules.GET("/:id", h.GetSchedule)                // 获取调度详情
		schedules.PUT("/:id", h.UpdateSchedule)             // 更新调度
		schedules.DELETE("/:id", h.DeleteSchedule)          // 删除调度
		schedules.POST("/:id/trigger", h.TriggerSchedule)   // 手动触发调度
	}

	// 统计相关路由
	stats := router.Group("/script-stats")
	{
		stats.GET("/overview", h.GetStatsOverview)          // 获取统计概览
		stats.GET("/metrics", h.GetMetrics)                 // 获取指标数据
	}
}

// ExecuteScript 执行脚本
func (h *Handler) ExecuteScript(c *gin.Context) {
	var req ExecuteScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数格式错误",
			Details: err.Error(),
		})
		return
	}

	// 从上下文获取用户和租户信息
	userID, _ := c.Get("user_id")
	tenantID, _ := c.Get("tenant_id")

	// 构建执行请求
	execReq := &ExecutionRequest{
		AppID:       req.AppID,
		ScriptPath:  req.ScriptPath,
		Parameters:  req.Parameters,
		Environment: req.Environment,
		Timeout:     time.Duration(req.Timeout) * time.Second,
		Priority:    req.Priority,
		Callback:    req.Callback,
		UserID:      userID.(string),
		TenantID:    tenantID.(string),
	}

	// 提交执行任务
	task, err := h.service.SubmitTask(c.Request.Context(), execReq)
	if err != nil {
		h.logger.Error("提交脚本执行任务失败", "error", err, "app_id", req.AppID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "SUBMIT_TASK_FAILED",
			Message: "提交执行任务失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("脚本执行任务已提交", "task_id", task.ID, "app_id", req.AppID)
	c.JSON(http.StatusCreated, ExecuteScriptResponse{
		TaskID:    task.ID,
		Status:    string(task.Status),
		Message:   "任务已提交",
		CreatedAt: task.CreatedAt,
	})
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	status, err := h.service.GetTaskStatus(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务状态失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TASK_NOT_FOUND",
			Message: "任务不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, status)
}

// GetTaskResult 获取任务结果
func (h *Handler) GetTaskResult(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	result, err := h.service.GetTaskResult(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务结果失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "TASK_NOT_FOUND",
			Message: "任务不存在",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ListTasks 获取任务列表
func (h *Handler) ListTasks(c *gin.Context) {
	// 解析查询参数
	filter := &TaskFilter{
		AppID:    c.Query("app_id"),
		Status:   TaskStatus(c.Query("status")),
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	// 从上下文获取租户信息
	tenantID, _ := c.Get("tenant_id")
	filter.TenantID = tenantID.(string)

	// 查询任务列表
	tasks, total, err := h.service.ListTasks(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("获取任务列表失败", "error", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_TASKS_FAILED",
			Message: "获取任务列表失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ListTasksResponse{
		Tasks: tasks,
		Pagination: PaginationResponse{
			Page:     filter.Page,
			PageSize: filter.PageSize,
			Total:    total,
		},
	})
}

// GetTaskLogs 获取任务日志
func (h *Handler) GetTaskLogs(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	logs, err := h.service.GetExecutionLogs(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("获取任务日志失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_LOGS_FAILED",
			Message: "获取任务日志失败",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, GetTaskLogsResponse{
		TaskID: taskID,
		Logs:   logs,
	})
}

// CancelTask 取消任务
func (h *Handler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_TASK_ID",
			Message: "任务ID不能为空",
		})
		return
	}

	err := h.service.CancelTask(c.Request.Context(), taskID)
	if err != nil {
		h.logger.Error("取消任务失败", "error", err, "task_id", taskID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CANCEL_TASK_FAILED",
			Message: "取消任务失败",
			Details: err.Error(),
		})
		return
	}

	h.logger.Info("任务已取消", "task_id", taskID)
	c.JSON(http.StatusOK, gin.H{
		"message": "任务已取消",
		"task_id": taskID,
	})
}

// DownloadArtifact 下载产物
func (h *Handler) DownloadArtifact(c *gin.Context) {
	taskID := c.Param("id")
	artifactName := c.Param("name")

	if taskID == "" || artifactName == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_PARAMETERS",
			Message: "任务ID和产物名称不能为空",
		})
		return
	}

	// TODO: 实现产物下载逻辑
	// 1. 验证任务存在和权限
	// 2. 获取产物文件路径
	// 3. 返回文件流

	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "产物下载功能开发中",
	})
}

// CreateTemplate 创建脚本模板
func (h *Handler) CreateTemplate(c *gin.Context) {
	// TODO: 实现脚本模板创建逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "脚本模板功能开发中",
	})
}

// ListTemplates 获取模板列表
func (h *Handler) ListTemplates(c *gin.Context) {
	// TODO: 实现模板列表查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "脚本模板功能开发中",
	})
}

// GetTemplate 获取模板详情
func (h *Handler) GetTemplate(c *gin.Context) {
	// TODO: 实现模板详情查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "脚本模板功能开发中",
	})
}

// UpdateTemplate 更新模板
func (h *Handler) UpdateTemplate(c *gin.Context) {
	// TODO: 实现模板更新逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "脚本模板功能开发中",
	})
}

// DeleteTemplate 删除模板
func (h *Handler) DeleteTemplate(c *gin.Context) {
	// TODO: 实现模板删除逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "脚本模板功能开发中",
	})
}

// CreateSchedule 创建任务调度
func (h *Handler) CreateSchedule(c *gin.Context) {
	// TODO: 实现任务调度创建逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// ListSchedules 获取调度列表
func (h *Handler) ListSchedules(c *gin.Context) {
	// TODO: 实现调度列表查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// GetSchedule 获取调度详情
func (h *Handler) GetSchedule(c *gin.Context) {
	// TODO: 实现调度详情查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// UpdateSchedule 更新调度
func (h *Handler) UpdateSchedule(c *gin.Context) {
	// TODO: 实现调度更新逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// DeleteSchedule 删除调度
func (h *Handler) DeleteSchedule(c *gin.Context) {
	// TODO: 实现调度删除逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// TriggerSchedule 手动触发调度
func (h *Handler) TriggerSchedule(c *gin.Context) {
	// TODO: 实现手动触发调度逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "任务调度功能开发中",
	})
}

// GetStatsOverview 获取统计概览
func (h *Handler) GetStatsOverview(c *gin.Context) {
	// TODO: 实现统计概览查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "统计功能开发中",
	})
}

// GetMetrics 获取指标数据
func (h *Handler) GetMetrics(c *gin.Context) {
	// TODO: 实现指标数据查询逻辑
	c.JSON(http.StatusNotImplemented, ErrorResponse{
		Code:    "NOT_IMPLEMENTED",
		Message: "指标功能开发中",
	})
}
