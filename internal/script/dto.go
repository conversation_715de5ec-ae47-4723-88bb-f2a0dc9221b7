package script

import (
	"time"
)

// ExecuteScriptRequest 执行脚本请求
type ExecuteScriptRequest struct {
	AppID       string                 `json:"app_id" binding:"required" example:"app-123"`
	ScriptPath  string                 `json:"script_path" binding:"required" example:"main.py"`
	Parameters  map[string]interface{} `json:"parameters" example:"{\"input_file\":\"data.csv\",\"output_format\":\"json\"}"`
	Environment map[string]string      `json:"environment" example:"{\"PYTHONPATH\":\"/app/lib\"}"`
	Timeout     int                    `json:"timeout" example:"300"` // 秒
	Priority    int                    `json:"priority" example:"5"`  // 1-10
	Callback    string                 `json:"callback" example:"https://example.com/webhook"`
}

// ExecuteScriptResponse 执行脚本响应
type ExecuteScriptResponse struct {
	TaskID    string    `json:"task_id" example:"task-456"`
	Status    string    `json:"status" example:"pending"`
	Message   string    `json:"message" example:"任务已提交"`
	CreatedAt time.Time `json:"created_at" example:"2024-01-01T10:00:00Z"`
}

// ListTasksResponse 任务列表响应
type ListTasksResponse struct {
	Tasks      []*Task            `json:"tasks"`
	Pagination PaginationResponse `json:"pagination"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page     int   `json:"page" example:"1"`
	PageSize int   `json:"page_size" example:"20"`
	Total    int64 `json:"total" example:"100"`
}

// GetTaskLogsResponse 获取任务日志响应
type GetTaskLogsResponse struct {
	TaskID string     `json:"task_id" example:"task-456"`
	Logs   []LogEntry `json:"logs"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    string `json:"code" example:"INVALID_REQUEST"`
	Message string `json:"message" example:"请求参数错误"`
	Details string `json:"details,omitempty" example:"字段验证失败"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name        string                 `json:"name" binding:"required" example:"数据处理模板"`
	Description string                 `json:"description" example:"用于数据清洗和转换的Python脚本模板"`
	Category    string                 `json:"category" example:"data-processing"`
	Language    string                 `json:"language" binding:"required" example:"python"`
	Version     string                 `json:"version" binding:"required" example:"1.0.0"`
	Content     string                 `json:"content" binding:"required" example:"import pandas as pd\\n..."`
	Parameters  map[string]interface{} `json:"parameters" example:"{\"input_file\":{\"type\":\"string\",\"required\":true}}"`
	Environment map[string]string      `json:"environment" example:"{\"PYTHONPATH\":\"/app/lib\"}"`
	Resources   map[string]interface{} `json:"resources" example:"{\"cpu\":\"500m\",\"memory\":\"512Mi\"}"`
	Tags        []string               `json:"tags" example:"[\"data\",\"etl\",\"pandas\"]"`
}

// CreateTemplateResponse 创建模板响应
type CreateTemplateResponse struct {
	ID        string    `json:"id" example:"template-123"`
	Name      string    `json:"name" example:"数据处理模板"`
	Version   string    `json:"version" example:"1.0.0"`
	Status    string    `json:"status" example:"active"`
	CreatedAt time.Time `json:"created_at" example:"2024-01-01T10:00:00Z"`
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Name        string                 `json:"name" example:"数据处理模板v2"`
	Description string                 `json:"description" example:"更新的数据处理模板"`
	Content     string                 `json:"content" example:"import pandas as pd\\n..."`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	Resources   map[string]interface{} `json:"resources"`
	Tags        []string               `json:"tags"`
	Status      string                 `json:"status" example:"active"`
}

// ListTemplatesResponse 模板列表响应
type ListTemplatesResponse struct {
	Templates  []*ScriptTemplate  `json:"templates"`
	Pagination PaginationResponse `json:"pagination"`
}

// CreateScheduleRequest 创建调度请求
type CreateScheduleRequest struct {
	Name        string                 `json:"name" binding:"required" example:"每日数据处理"`
	Description string                 `json:"description" example:"每天凌晨执行数据处理任务"`
	AppID       string                 `json:"app_id" binding:"required" example:"app-123"`
	ScriptPath  string                 `json:"script_path" binding:"required" example:"daily_process.py"`
	Parameters  map[string]interface{} `json:"parameters" example:"{\"date\":\"{{.Date}}\"}"`
	Environment map[string]string      `json:"environment" example:"{\"ENV\":\"production\"}"`
	CronExpr    string                 `json:"cron_expr" binding:"required" example:"0 0 * * *"`
	Timezone    string                 `json:"timezone" example:"Asia/Shanghai"`
}

// CreateScheduleResponse 创建调度响应
type CreateScheduleResponse struct {
	ID        string     `json:"id" example:"schedule-123"`
	Name      string     `json:"name" example:"每日数据处理"`
	Status    string     `json:"status" example:"active"`
	NextRun   *time.Time `json:"next_run" example:"2024-01-02T00:00:00Z"`
	CreatedAt time.Time  `json:"created_at" example:"2024-01-01T10:00:00Z"`
}

// UpdateScheduleRequest 更新调度请求
type UpdateScheduleRequest struct {
	Name        string                 `json:"name" example:"每日数据处理v2"`
	Description string                 `json:"description" example:"更新的每日数据处理任务"`
	Parameters  map[string]interface{} `json:"parameters"`
	Environment map[string]string      `json:"environment"`
	CronExpr    string                 `json:"cron_expr" example:"0 1 * * *"`
	Timezone    string                 `json:"timezone" example:"Asia/Shanghai"`
	Status      string                 `json:"status" example:"active"`
}

// ListSchedulesResponse 调度列表响应
type ListSchedulesResponse struct {
	Schedules  []*TaskSchedule    `json:"schedules"`
	Pagination PaginationResponse `json:"pagination"`
}

// TriggerScheduleRequest 触发调度请求
type TriggerScheduleRequest struct {
	Parameters  map[string]interface{} `json:"parameters" example:"{\"force\":true}"`
	Environment map[string]string      `json:"environment" example:"{\"MANUAL_RUN\":\"true\"}"`
}

// TriggerScheduleResponse 触发调度响应
type TriggerScheduleResponse struct {
	TaskID      string    `json:"task_id" example:"task-789"`
	ScheduleID  string    `json:"schedule_id" example:"schedule-123"`
	Status      string    `json:"status" example:"pending"`
	TriggeredAt time.Time `json:"triggered_at" example:"2024-01-01T10:00:00Z"`
}

// StatsOverviewResponse 统计概览响应
type StatsOverviewResponse struct {
	Today     TaskStatsData `json:"today"`
	Yesterday TaskStatsData `json:"yesterday"`
	ThisWeek  TaskStatsData `json:"this_week"`
	ThisMonth TaskStatsData `json:"this_month"`
}

// TaskStatsData 任务统计数据
type TaskStatsData struct {
	TotalTasks     int     `json:"total_tasks" example:"100"`
	CompletedTasks int     `json:"completed_tasks" example:"85"`
	FailedTasks    int     `json:"failed_tasks" example:"10"`
	CanceledTasks  int     `json:"canceled_tasks" example:"5"`
	SuccessRate    float64 `json:"success_rate" example:"85.0"`
	AvgDuration    float64 `json:"avg_duration" example:"120.5"` // 秒
}

// MetricsResponse 指标响应
type MetricsResponse struct {
	TimeRange string        `json:"time_range" example:"24h"`
	Interval  string        `json:"interval" example:"1h"`
	Metrics   []MetricPoint `json:"metrics"`
}

// MetricPoint 指标点
type MetricPoint struct {
	Timestamp   time.Time `json:"timestamp" example:"2024-01-01T10:00:00Z"`
	TaskCount   int       `json:"task_count" example:"10"`
	SuccessRate float64   `json:"success_rate" example:"90.0"`
	AvgDuration float64   `json:"avg_duration" example:"115.2"`
	CPUUsage    float64   `json:"cpu_usage" example:"45.5"`
	MemoryUsage int64     `json:"memory_usage" example:"536870912"`
}

// ArtifactInfo 产物信息
type ArtifactInfo struct {
	Name         string    `json:"name" example:"result.json"`
	Size         int64     `json:"size" example:"1024"`
	MimeType     string    `json:"mime_type" example:"application/json"`
	DownloadURL  string    `json:"download_url" example:"/api/v1/scripts/tasks/task-456/artifacts/result.json"`
	CreatedAt    time.Time `json:"created_at" example:"2024-01-01T10:05:00Z"`
}

// TaskDetailResponse 任务详情响应
type TaskDetailResponse struct {
	Task      *Task          `json:"task"`
	Artifacts []ArtifactInfo `json:"artifacts"`
	Metrics   []MetricPoint  `json:"metrics"`
}

// NodeStatusResponse 节点状态响应
type NodeStatusResponse struct {
	Nodes []*ExecutionNode `json:"nodes"`
	Stats NodeStatsData    `json:"stats"`
}

// NodeStatsData 节点统计数据
type NodeStatsData struct {
	TotalNodes   int `json:"total_nodes" example:"5"`
	OnlineNodes  int `json:"online_nodes" example:"4"`
	OfflineNodes int `json:"offline_nodes" example:"1"`
	BusyNodes    int `json:"busy_nodes" example:"2"`
	TotalLoad    int `json:"total_load" example:"25"`
	MaxCapacity  int `json:"max_capacity" example:"50"`
}

// CallbackRequest 回调请求
type CallbackRequest struct {
	TaskID      string                 `json:"task_id" example:"task-456"`
	Status      string                 `json:"status" example:"completed"`
	ExitCode    int                    `json:"exit_code" example:"0"`
	Output      string                 `json:"output" example:"Processing completed successfully"`
	Error       string                 `json:"error,omitempty" example:""`
	Duration    int64                  `json:"duration" example:"245000"` // 毫秒
	StartTime   time.Time              `json:"start_time" example:"2024-01-01T10:00:00Z"`
	EndTime     time.Time              `json:"end_time" example:"2024-01-01T10:04:05Z"`
	Resources   ResourceUsage          `json:"resources"`
	Artifacts   []ArtifactInfo         `json:"artifacts"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// WebhookResponse 回调响应
type WebhookResponse struct {
	Success   bool   `json:"success" example:"true"`
	Message   string `json:"message" example:"回调处理成功"`
	Timestamp time.Time `json:"timestamp" example:"2024-01-01T10:04:10Z"`
}

// BatchOperationRequest 批量操作请求
type BatchOperationRequest struct {
	TaskIDs   []string `json:"task_ids" binding:"required" example:"[\"task-1\",\"task-2\",\"task-3\"]"`
	Operation string   `json:"operation" binding:"required" example:"cancel"` // cancel, retry, delete
}

// BatchOperationResponse 批量操作响应
type BatchOperationResponse struct {
	Success []string `json:"success" example:"[\"task-1\",\"task-2\"]"`
	Failed  []string `json:"failed" example:"[\"task-3\"]"`
	Message string   `json:"message" example:"批量操作完成"`
}

// ExportTasksRequest 导出任务请求
type ExportTasksRequest struct {
	Format    string     `json:"format" example:"csv"`        // csv, json, xlsx
	Filter    TaskFilter `json:"filter"`
	StartDate *time.Time `json:"start_date" example:"2024-01-01T00:00:00Z"`
	EndDate   *time.Time `json:"end_date" example:"2024-01-31T23:59:59Z"`
}

// ExportTasksResponse 导出任务响应
type ExportTasksResponse struct {
	DownloadURL string    `json:"download_url" example:"/api/v1/scripts/exports/tasks-20240101.csv"`
	ExpiresAt   time.Time `json:"expires_at" example:"2024-01-02T10:00:00Z"`
	FileSize    int64     `json:"file_size" example:"2048"`
}
