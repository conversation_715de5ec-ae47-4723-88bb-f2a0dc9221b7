package container

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/client"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ContainerManager 容器管理器接口
type ContainerManager interface {
	// 创建执行容器
	CreateContainer(ctx context.Context, spec *ContainerSpec) (*Container, error)
	
	// 启动容器
	StartContainer(ctx context.Context, containerID string) error
	
	// 停止并删除容器
	DestroyContainer(ctx context.Context, containerID string) error
	
	// 获取容器状态
	GetContainerStatus(ctx context.Context, containerID string) (*ContainerStatus, error)
	
	// 预热镜像
	WarmupImage(ctx context.Context, imageTag string) error
	
	// 执行命令
	ExecuteCommand(ctx context.Context, containerID string, cmd []string) (*ExecutionResult, error)
	
	// 获取容器日志
	GetContainerLogs(ctx context.Context, containerID string) (string, error)
	
	// 清理过期容器
	CleanupExpiredContainers(ctx context.Context) error
}

// ContainerSpec 容器规格
type ContainerSpec struct {
	ID           string            `json:"id"`
	ImageTag     string            `json:"image_tag"`
	Command      []string          `json:"command"`
	WorkingDir   string            `json:"working_dir"`
	Environment  map[string]string `json:"environment"`
	Resources    ResourceLimits    `json:"resources"`
	Volumes      []VolumeMount     `json:"volumes"`
	NetworkMode  string            `json:"network_mode"`
	Timeout      time.Duration     `json:"timeout"`
	AutoRemove   bool              `json:"auto_remove"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	CPULimit    int64  `json:"cpu_limit"`     // CPU限制 (纳秒)
	MemoryLimit int64  `json:"memory_limit"`  // 内存限制 (字节)
	DiskLimit   int64  `json:"disk_limit"`    // 磁盘限制 (字节)
	PidsLimit   int64  `json:"pids_limit"`    // 进程数限制
}

// VolumeMount 卷挂载
type VolumeMount struct {
	Source      string `json:"source"`
	Target      string `json:"target"`
	ReadOnly    bool   `json:"read_only"`
}

// Container 容器信息
type Container struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	DockerID    string                 `json:"docker_id" gorm:"uniqueIndex"`
	ImageTag    string                 `json:"image_tag" gorm:"not null"`
	Status      ContainerStatus        `json:"status" gorm:"not null"`
	Spec        ContainerSpec          `json:"spec" gorm:"type:jsonb"`
	StartTime   *time.Time             `json:"start_time"`
	EndTime     *time.Time             `json:"end_time"`
	ExitCode    *int                   `json:"exit_code"`
	Error       string                 `json:"error" gorm:"type:text"`
	Resources   ResourceUsage          `json:"resources" gorm:"type:jsonb"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ContainerStatus 容器状态
type ContainerStatus string

const (
	ContainerStatusCreated   ContainerStatus = "created"
	ContainerStatusStarting  ContainerStatus = "starting"
	ContainerStatusRunning   ContainerStatus = "running"
	ContainerStatusExited    ContainerStatus = "exited"
	ContainerStatusFailed    ContainerStatus = "failed"
	ContainerStatusDestroyed ContainerStatus = "destroyed"
)

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率 (%)
	MemoryUsage int64   `json:"memory_usage"` // 内存使用量 (字节)
	DiskUsage   int64   `json:"disk_usage"`   // 磁盘使用量 (字节)
	NetworkRx   int64   `json:"network_rx"`   // 网络接收字节
	NetworkTx   int64   `json:"network_tx"`   // 网络发送字节
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ExitCode int    `json:"exit_code"`
	Output   string `json:"output"`
	Error    string `json:"error"`
	Duration int64  `json:"duration"` // 毫秒
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
}

// containerManager 容器管理器实现
type containerManager struct {
	db           *gorm.DB
	dockerClient *client.Client
	logger       Logger
	config       ManagerConfig
	containers   sync.Map // 容器缓存
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	DefaultTimeout    time.Duration `json:"default_timeout"`
	MaxContainers     int           `json:"max_containers"`
	CleanupInterval   time.Duration `json:"cleanup_interval"`
	ImagePullTimeout  time.Duration `json:"image_pull_timeout"`
	ContainerNetwork  string        `json:"container_network"`
	DefaultResources  ResourceLimits `json:"default_resources"`
}

// NewContainerManager 创建容器管理器
func NewContainerManager(db *gorm.DB, logger Logger, config ManagerConfig) (ContainerManager, error) {
	// 创建Docker客户端
	dockerClient, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return nil, fmt.Errorf("创建Docker客户端失败: %w", err)
	}

	manager := &containerManager{
		db:           db,
		dockerClient: dockerClient,
		logger:       logger,
		config:       config,
	}

	// 启动清理协程
	go manager.startCleanupRoutine()

	return manager, nil
}

// CreateContainer 创建执行容器
func (m *containerManager) CreateContainer(ctx context.Context, spec *ContainerSpec) (*Container, error) {
	// 生成容器ID
	if spec.ID == "" {
		spec.ID = uuid.New().String()
	}

	// 检查镜像是否存在，不存在则拉取
	if err := m.ensureImage(ctx, spec.ImageTag); err != nil {
		return nil, fmt.Errorf("确保镜像存在失败: %w", err)
	}

	// 构建容器配置
	containerConfig := &container.Config{
		Image:        spec.ImageTag,
		Cmd:          spec.Command,
		WorkingDir:   spec.WorkingDir,
		Env:          m.buildEnvList(spec.Environment),
		AttachStdout: true,
		AttachStderr: true,
	}

	// 构建主机配置
	hostConfig := &container.HostConfig{
		AutoRemove: spec.AutoRemove,
		Resources: container.Resources{
			CPUQuota:  spec.Resources.CPULimit,
			Memory:    spec.Resources.MemoryLimit,
			PidsLimit: &spec.Resources.PidsLimit,
		},
		NetworkMode: container.NetworkMode(spec.NetworkMode),
	}

	// 添加卷挂载
	for _, volume := range spec.Volumes {
		hostConfig.Binds = append(hostConfig.Binds, fmt.Sprintf("%s:%s:%s", 
			volume.Source, volume.Target, m.getMountMode(volume.ReadOnly)))
	}

	// 创建容器
	resp, err := m.dockerClient.ContainerCreate(ctx, containerConfig, hostConfig, &network.NetworkingConfig{}, nil, "")
	if err != nil {
		return nil, fmt.Errorf("创建容器失败: %w", err)
	}

	// 创建容器记录
	containerRecord := &Container{
		ID:        spec.ID,
		DockerID:  resp.ID,
		ImageTag:  spec.ImageTag,
		Status:    ContainerStatusCreated,
		Spec:      *spec,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存到数据库
	if err := m.db.Create(containerRecord).Error; err != nil {
		// 如果数据库保存失败，清理Docker容器
		m.dockerClient.ContainerRemove(ctx, resp.ID, types.ContainerRemoveOptions{Force: true})
		return nil, fmt.Errorf("保存容器记录失败: %w", err)
	}

	// 缓存容器信息
	m.containers.Store(spec.ID, containerRecord)

	m.logger.Info("容器创建成功", "container_id", spec.ID, "docker_id", resp.ID, "image", spec.ImageTag)
	return containerRecord, nil
}

// StartContainer 启动容器
func (m *containerManager) StartContainer(ctx context.Context, containerID string) error {
	// 获取容器信息
	containerRecord, err := m.getContainer(containerID)
	if err != nil {
		return err
	}

	// 启动容器
	if err := m.dockerClient.ContainerStart(ctx, containerRecord.DockerID, types.ContainerStartOptions{}); err != nil {
		m.updateContainerStatus(containerID, ContainerStatusFailed, err.Error())
		return fmt.Errorf("启动容器失败: %w", err)
	}

	// 更新状态
	startTime := time.Now()
	m.updateContainerStatus(containerID, ContainerStatusRunning, "")
	m.db.Model(&Container{}).Where("id = ?", containerID).Update("start_time", &startTime)

	m.logger.Info("容器启动成功", "container_id", containerID, "docker_id", containerRecord.DockerID)
	return nil
}

// DestroyContainer 停止并删除容器
func (m *containerManager) DestroyContainer(ctx context.Context, containerID string) error {
	// 获取容器信息
	containerRecord, err := m.getContainer(containerID)
	if err != nil {
		return err
	}

	// 停止容器
	timeout := int(m.config.DefaultTimeout.Seconds())
	if err := m.dockerClient.ContainerStop(ctx, containerRecord.DockerID, &timeout); err != nil {
		m.logger.Error("停止容器失败", "error", err, "container_id", containerID)
	}

	// 删除容器
	if err := m.dockerClient.ContainerRemove(ctx, containerRecord.DockerID, types.ContainerRemoveOptions{
		Force: true,
	}); err != nil {
		m.logger.Error("删除容器失败", "error", err, "container_id", containerID)
	}

	// 更新状态
	endTime := time.Now()
	m.updateContainerStatus(containerID, ContainerStatusDestroyed, "")
	m.db.Model(&Container{}).Where("id = ?", containerID).Update("end_time", &endTime)

	// 从缓存中移除
	m.containers.Delete(containerID)

	m.logger.Info("容器销毁成功", "container_id", containerID, "docker_id", containerRecord.DockerID)
	return nil
}

// GetContainerStatus 获取容器状态
func (m *containerManager) GetContainerStatus(ctx context.Context, containerID string) (*ContainerStatus, error) {
	// 获取容器信息
	containerRecord, err := m.getContainer(containerID)
	if err != nil {
		return nil, err
	}

	// 获取Docker容器状态
	inspect, err := m.dockerClient.ContainerInspect(ctx, containerRecord.DockerID)
	if err != nil {
		return nil, fmt.Errorf("获取容器状态失败: %w", err)
	}

	// 更新状态
	status := m.mapDockerStatus(inspect.State)
	m.updateContainerStatus(containerID, status, "")

	// 如果容器已退出，记录退出码
	if inspect.State.ExitCode != 0 {
		m.db.Model(&Container{}).Where("id = ?", containerID).Update("exit_code", inspect.State.ExitCode)
	}

	return &status, nil
}

// WarmupImage 预热镜像
func (m *containerManager) WarmupImage(ctx context.Context, imageTag string) error {
	return m.ensureImage(ctx, imageTag)
}

// ExecuteCommand 执行命令
func (m *containerManager) ExecuteCommand(ctx context.Context, containerID string, cmd []string) (*ExecutionResult, error) {
	// 获取容器信息
	containerRecord, err := m.getContainer(containerID)
	if err != nil {
		return nil, err
	}

	startTime := time.Now()

	// 创建执行配置
	execConfig := types.ExecConfig{
		Cmd:          cmd,
		AttachStdout: true,
		AttachStderr: true,
	}

	// 创建执行实例
	execResp, err := m.dockerClient.ContainerExecCreate(ctx, containerRecord.DockerID, execConfig)
	if err != nil {
		return nil, fmt.Errorf("创建执行实例失败: %w", err)
	}

	// 启动执行
	attachResp, err := m.dockerClient.ContainerExecAttach(ctx, execResp.ID, types.ExecStartCheck{})
	if err != nil {
		return nil, fmt.Errorf("启动执行失败: %w", err)
	}
	defer attachResp.Close()

	// 读取输出
	output, err := io.ReadAll(attachResp.Reader)
	if err != nil {
		return nil, fmt.Errorf("读取执行输出失败: %w", err)
	}

	// 获取执行结果
	execInspect, err := m.dockerClient.ContainerExecInspect(ctx, execResp.ID)
	if err != nil {
		return nil, fmt.Errorf("获取执行结果失败: %w", err)
	}

	duration := time.Since(startTime).Milliseconds()

	result := &ExecutionResult{
		ExitCode: execInspect.ExitCode,
		Output:   string(output),
		Duration: duration,
	}

	if execInspect.ExitCode != 0 {
		result.Error = string(output)
	}

	return result, nil
}

// GetContainerLogs 获取容器日志
func (m *containerManager) GetContainerLogs(ctx context.Context, containerID string) (string, error) {
	// 获取容器信息
	containerRecord, err := m.getContainer(containerID)
	if err != nil {
		return "", err
	}

	// 获取日志
	logs, err := m.dockerClient.ContainerLogs(ctx, containerRecord.DockerID, types.ContainerLogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Timestamps: true,
	})
	if err != nil {
		return "", fmt.Errorf("获取容器日志失败: %w", err)
	}
	defer logs.Close()

	// 读取日志内容
	logContent, err := io.ReadAll(logs)
	if err != nil {
		return "", fmt.Errorf("读取日志内容失败: %w", err)
	}

	return string(logContent), nil
}

// CleanupExpiredContainers 清理过期容器
func (m *containerManager) CleanupExpiredContainers(ctx context.Context) error {
	// 查找过期容器
	var expiredContainers []Container
	expiredTime := time.Now().Add(-24 * time.Hour) // 24小时前

	err := m.db.Where("status IN ? AND updated_at < ?", 
		[]ContainerStatus{ContainerStatusExited, ContainerStatusFailed}, expiredTime).
		Find(&expiredContainers).Error
	if err != nil {
		return fmt.Errorf("查询过期容器失败: %w", err)
	}

	// 清理过期容器
	for _, container := range expiredContainers {
		if err := m.DestroyContainer(ctx, container.ID); err != nil {
			m.logger.Error("清理过期容器失败", "error", err, "container_id", container.ID)
		}
	}

	m.logger.Info("过期容器清理完成", "count", len(expiredContainers))
	return nil
}

// 辅助方法

// ensureImage 确保镜像存在
func (m *containerManager) ensureImage(ctx context.Context, imageTag string) error {
	// 检查镜像是否存在
	_, _, err := m.dockerClient.ImageInspectWithRaw(ctx, imageTag)
	if err == nil {
		return nil // 镜像已存在
	}

	// 拉取镜像
	m.logger.Info("开始拉取镜像", "image", imageTag)
	
	pullCtx, cancel := context.WithTimeout(ctx, m.config.ImagePullTimeout)
	defer cancel()

	reader, err := m.dockerClient.ImagePull(pullCtx, imageTag, types.ImagePullOptions{})
	if err != nil {
		return fmt.Errorf("拉取镜像失败: %w", err)
	}
	defer reader.Close()

	// 等待拉取完成
	_, err = io.ReadAll(reader)
	if err != nil {
		return fmt.Errorf("等待镜像拉取完成失败: %w", err)
	}

	m.logger.Info("镜像拉取成功", "image", imageTag)
	return nil
}

// getContainer 获取容器信息
func (m *containerManager) getContainer(containerID string) (*Container, error) {
	// 先从缓存获取
	if cached, ok := m.containers.Load(containerID); ok {
		return cached.(*Container), nil
	}

	// 从数据库获取
	var container Container
	if err := m.db.Where("id = ?", containerID).First(&container).Error; err != nil {
		return nil, fmt.Errorf("容器不存在: %w", err)
	}

	// 加入缓存
	m.containers.Store(containerID, &container)
	return &container, nil
}

// updateContainerStatus 更新容器状态
func (m *containerManager) updateContainerStatus(containerID string, status ContainerStatus, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	m.db.Model(&Container{}).Where("id = ?", containerID).Updates(updates)

	// 更新缓存
	if cached, ok := m.containers.Load(containerID); ok {
		container := cached.(*Container)
		container.Status = status
		container.Error = errorMsg
		container.UpdatedAt = time.Now()
	}
}

// mapDockerStatus 映射Docker状态
func (m *containerManager) mapDockerStatus(state *types.ContainerState) ContainerStatus {
	if state.Running {
		return ContainerStatusRunning
	}
	if state.Paused {
		return ContainerStatusRunning
	}
	if state.Dead {
		return ContainerStatusFailed
	}
	if state.ExitCode != 0 {
		return ContainerStatusFailed
	}
	return ContainerStatusExited
}

// buildEnvList 构建环境变量列表
func (m *containerManager) buildEnvList(env map[string]string) []string {
	var envList []string
	for key, value := range env {
		envList = append(envList, fmt.Sprintf("%s=%s", key, value))
	}
	return envList
}

// getMountMode 获取挂载模式
func (m *containerManager) getMountMode(readOnly bool) string {
	if readOnly {
		return "ro"
	}
	return "rw"
}

// startCleanupRoutine 启动清理协程
func (m *containerManager) startCleanupRoutine() {
	ticker := time.NewTicker(m.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		ctx := context.Background()
		if err := m.CleanupExpiredContainers(ctx); err != nil {
			m.logger.Error("定时清理容器失败", "error", err)
		}
	}
}
