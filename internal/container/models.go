package container

import (
	"time"
	"gorm.io/datatypes"
)

// ContainerPool 容器池
type ContainerPool struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"not null;uniqueIndex"`
	ImageTag    string                 `json:"image_tag" gorm:"not null"`
	MinSize     int                    `json:"min_size" gorm:"default:0"`
	MaxSize     int                    `json:"max_size" gorm:"default:10"`
	CurrentSize int                    `json:"current_size" gorm:"default:0"`
	Config      ContainerPoolConfig    `json:"config" gorm:"type:jsonb"`
	Status      ContainerPoolStatus    `json:"status" gorm:"not null"`
	TenantID    string                 `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ContainerPoolStatus 容器池状态
type ContainerPoolStatus string

const (
	ContainerPoolStatusActive   ContainerPoolStatus = "active"
	ContainerPoolStatusInactive ContainerPoolStatus = "inactive"
	ContainerPoolStatusScaling  ContainerPoolStatus = "scaling"
	ContainerPoolStatusError    ContainerPoolStatus = "error"
)

// ContainerPoolConfig 容器池配置
type ContainerPoolConfig struct {
	Resources       ResourceLimits    `json:"resources"`
	Environment     map[string]string `json:"environment"`
	Volumes         []VolumeMount     `json:"volumes"`
	NetworkMode     string            `json:"network_mode"`
	WarmupTimeout   int               `json:"warmup_timeout"`   // 预热超时时间(秒)
	IdleTimeout     int               `json:"idle_timeout"`     // 空闲超时时间(秒)
	HealthCheck     HealthCheckConfig `json:"health_check"`
	AutoScale       AutoScaleConfig   `json:"auto_scale"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled         bool   `json:"enabled"`
	Command         string `json:"command"`
	Interval        int    `json:"interval"`        // 检查间隔(秒)
	Timeout         int    `json:"timeout"`         // 超时时间(秒)
	Retries         int    `json:"retries"`         // 重试次数
	StartPeriod     int    `json:"start_period"`    // 启动等待时间(秒)
}

// AutoScaleConfig 自动扩缩容配置
type AutoScaleConfig struct {
	Enabled                bool    `json:"enabled"`
	TargetCPUUtilization   float64 `json:"target_cpu_utilization"`    // 目标CPU使用率
	TargetMemoryUtilization float64 `json:"target_memory_utilization"` // 目标内存使用率
	ScaleUpCooldown        int     `json:"scale_up_cooldown"`          // 扩容冷却时间(秒)
	ScaleDownCooldown      int     `json:"scale_down_cooldown"`        // 缩容冷却时间(秒)
	ScaleUpThreshold       int     `json:"scale_up_threshold"`         // 扩容阈值(连续次数)
	ScaleDownThreshold     int     `json:"scale_down_threshold"`       // 缩容阈值(连续次数)
}

// ContainerMetrics 容器指标
type ContainerMetrics struct {
	ID            string        `json:"id" gorm:"primaryKey"`
	ContainerID   string        `json:"container_id" gorm:"not null;index"`
	Timestamp     time.Time     `json:"timestamp" gorm:"not null;index"`
	CPUUsage      float64       `json:"cpu_usage"`      // CPU使用率 (%)
	MemoryUsage   int64         `json:"memory_usage"`   // 内存使用量 (字节)
	MemoryLimit   int64         `json:"memory_limit"`   // 内存限制 (字节)
	DiskUsage     int64         `json:"disk_usage"`     // 磁盘使用量 (字节)
	NetworkRx     int64         `json:"network_rx"`     // 网络接收字节
	NetworkTx     int64         `json:"network_tx"`     // 网络发送字节
	ProcessCount  int           `json:"process_count"`  // 进程数量
	FileDescCount int           `json:"file_desc_count"` // 文件描述符数量
	CreatedAt     time.Time     `json:"created_at"`
}

// ContainerEvent 容器事件
type ContainerEvent struct {
	ID          string              `json:"id" gorm:"primaryKey"`
	ContainerID string              `json:"container_id" gorm:"not null;index"`
	EventType   ContainerEventType  `json:"event_type" gorm:"not null"`
	Message     string              `json:"message" gorm:"type:text"`
	Data        datatypes.JSON      `json:"data"`
	Timestamp   time.Time           `json:"timestamp" gorm:"not null;index"`
	CreatedAt   time.Time           `json:"created_at"`
}

// ContainerEventType 容器事件类型
type ContainerEventType string

const (
	ContainerEventCreated   ContainerEventType = "created"
	ContainerEventStarted   ContainerEventType = "started"
	ContainerEventStopped   ContainerEventType = "stopped"
	ContainerEventDestroyed ContainerEventType = "destroyed"
	ContainerEventFailed    ContainerEventType = "failed"
	ContainerEventHealthy   ContainerEventType = "healthy"
	ContainerEventUnhealthy ContainerEventType = "unhealthy"
	ContainerEventScaled    ContainerEventType = "scaled"
)

// ContainerImage 容器镜像
type ContainerImage struct {
	ID          string             `json:"id" gorm:"primaryKey"`
	Tag         string             `json:"tag" gorm:"not null;uniqueIndex"`
	Digest      string             `json:"digest" gorm:"not null"`
	Size        int64              `json:"size"`
	Architecture string            `json:"architecture"`
	OS          string             `json:"os"`
	Labels      datatypes.JSON     `json:"labels"`
	Config      ImageConfig        `json:"config" gorm:"type:jsonb"`
	Status      ContainerImageStatus `json:"status" gorm:"not null"`
	PullCount   int                `json:"pull_count" gorm:"default:0"`
	LastPulled  *time.Time         `json:"last_pulled"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
}

// ContainerImageStatus 容器镜像状态
type ContainerImageStatus string

const (
	ContainerImageStatusAvailable ContainerImageStatus = "available"
	ContainerImageStatusPulling   ContainerImageStatus = "pulling"
	ContainerImageStatusFailed    ContainerImageStatus = "failed"
	ContainerImageStatusDeleted   ContainerImageStatus = "deleted"
)

// ImageConfig 镜像配置
type ImageConfig struct {
	Env         []string          `json:"env"`
	Cmd         []string          `json:"cmd"`
	Entrypoint  []string          `json:"entrypoint"`
	WorkingDir  string            `json:"working_dir"`
	User        string            `json:"user"`
	ExposedPorts map[string]struct{} `json:"exposed_ports"`
	Volumes     map[string]struct{} `json:"volumes"`
}

// ContainerTemplate 容器模板
type ContainerTemplate struct {
	ID          string                    `json:"id" gorm:"primaryKey"`
	Name        string                    `json:"name" gorm:"not null"`
	Description string                    `json:"description" gorm:"type:text"`
	ImageTag    string                    `json:"image_tag" gorm:"not null"`
	Config      ContainerTemplateConfig   `json:"config" gorm:"type:jsonb"`
	Category    string                    `json:"category" gorm:"index"`
	Tags        datatypes.JSON            `json:"tags"`
	Status      ContainerTemplateStatus   `json:"status" gorm:"not null"`
	Version     string                    `json:"version" gorm:"not null"`
	TenantID    string                    `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string                    `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time                 `json:"created_at"`
	UpdatedAt   time.Time                 `json:"updated_at"`
}

// ContainerTemplateStatus 容器模板状态
type ContainerTemplateStatus string

const (
	ContainerTemplateStatusActive   ContainerTemplateStatus = "active"
	ContainerTemplateStatusInactive ContainerTemplateStatus = "inactive"
	ContainerTemplateStatusDraft    ContainerTemplateStatus = "draft"
)

// ContainerTemplateConfig 容器模板配置
type ContainerTemplateConfig struct {
	Command      []string          `json:"command"`
	WorkingDir   string            `json:"working_dir"`
	Environment  map[string]string `json:"environment"`
	Resources    ResourceLimits    `json:"resources"`
	Volumes      []VolumeMount     `json:"volumes"`
	NetworkMode  string            `json:"network_mode"`
	HealthCheck  HealthCheckConfig `json:"health_check"`
	AutoRemove   bool              `json:"auto_remove"`
	Privileged   bool              `json:"privileged"`
	ReadOnly     bool              `json:"read_only"`
	SecurityOpts []string          `json:"security_opts"`
}

// ContainerNetwork 容器网络
type ContainerNetwork struct {
	ID          string                  `json:"id" gorm:"primaryKey"`
	Name        string                  `json:"name" gorm:"not null;uniqueIndex"`
	Driver      string                  `json:"driver" gorm:"not null"`
	Scope       string                  `json:"scope"`
	Config      ContainerNetworkConfig  `json:"config" gorm:"type:jsonb"`
	Status      ContainerNetworkStatus  `json:"status" gorm:"not null"`
	TenantID    string                  `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
}

// ContainerNetworkStatus 容器网络状态
type ContainerNetworkStatus string

const (
	ContainerNetworkStatusActive   ContainerNetworkStatus = "active"
	ContainerNetworkStatusInactive ContainerNetworkStatus = "inactive"
	ContainerNetworkStatusError    ContainerNetworkStatus = "error"
)

// ContainerNetworkConfig 容器网络配置
type ContainerNetworkConfig struct {
	Subnet     string            `json:"subnet"`
	Gateway    string            `json:"gateway"`
	IPRange    string            `json:"ip_range"`
	Options    map[string]string `json:"options"`
	Labels     map[string]string `json:"labels"`
	EnableIPv6 bool              `json:"enable_ipv6"`
	Internal   bool              `json:"internal"`
	Attachable bool              `json:"attachable"`
}

// ContainerVolume 容器卷
type ContainerVolume struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"not null;uniqueIndex"`
	Driver      string                 `json:"driver" gorm:"not null"`
	MountPoint  string                 `json:"mount_point" gorm:"not null"`
	Config      ContainerVolumeConfig  `json:"config" gorm:"type:jsonb"`
	Status      ContainerVolumeStatus  `json:"status" gorm:"not null"`
	Size        int64                  `json:"size"`
	UsedSize    int64                  `json:"used_size"`
	TenantID    string                 `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ContainerVolumeStatus 容器卷状态
type ContainerVolumeStatus string

const (
	ContainerVolumeStatusActive   ContainerVolumeStatus = "active"
	ContainerVolumeStatusInactive ContainerVolumeStatus = "inactive"
	ContainerVolumeStatusError    ContainerVolumeStatus = "error"
)

// ContainerVolumeConfig 容器卷配置
type ContainerVolumeConfig struct {
	Options map[string]string `json:"options"`
	Labels  map[string]string `json:"labels"`
}

// ContainerRegistry 容器镜像仓库
type ContainerRegistry struct {
	ID          string                    `json:"id" gorm:"primaryKey"`
	Name        string                    `json:"name" gorm:"not null;uniqueIndex"`
	URL         string                    `json:"url" gorm:"not null"`
	Type        ContainerRegistryType     `json:"type" gorm:"not null"`
	Config      ContainerRegistryConfig   `json:"config" gorm:"type:jsonb"`
	Status      ContainerRegistryStatus   `json:"status" gorm:"not null"`
	TenantID    string                    `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time                 `json:"created_at"`
	UpdatedAt   time.Time                 `json:"updated_at"`
}

// ContainerRegistryType 容器镜像仓库类型
type ContainerRegistryType string

const (
	ContainerRegistryTypeDocker   ContainerRegistryType = "docker"
	ContainerRegistryTypeHarbor   ContainerRegistryType = "harbor"
	ContainerRegistryTypeAWS      ContainerRegistryType = "aws"
	ContainerRegistryTypeAliyun   ContainerRegistryType = "aliyun"
	ContainerRegistryTypeTencent  ContainerRegistryType = "tencent"
)

// ContainerRegistryStatus 容器镜像仓库状态
type ContainerRegistryStatus string

const (
	ContainerRegistryStatusActive   ContainerRegistryStatus = "active"
	ContainerRegistryStatusInactive ContainerRegistryStatus = "inactive"
	ContainerRegistryStatusError    ContainerRegistryStatus = "error"
)

// ContainerRegistryConfig 容器镜像仓库配置
type ContainerRegistryConfig struct {
	Username    string `json:"username"`
	Password    string `json:"password"`
	Email       string `json:"email"`
	Insecure    bool   `json:"insecure"`
	CACert      string `json:"ca_cert"`
	ClientCert  string `json:"client_cert"`
	ClientKey   string `json:"client_key"`
}

// ContainerLog 容器日志
type ContainerLog struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	ContainerID string    `json:"container_id" gorm:"not null;index"`
	Level       string    `json:"level" gorm:"index"`
	Message     string    `json:"message" gorm:"type:text"`
	Source      string    `json:"source"`
	Timestamp   time.Time `json:"timestamp" gorm:"not null;index"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (Container) TableName() string {
	return "containers"
}

func (ContainerPool) TableName() string {
	return "container_pools"
}

func (ContainerMetrics) TableName() string {
	return "container_metrics"
}

func (ContainerEvent) TableName() string {
	return "container_events"
}

func (ContainerImage) TableName() string {
	return "container_images"
}

func (ContainerTemplate) TableName() string {
	return "container_templates"
}

func (ContainerNetwork) TableName() string {
	return "container_networks"
}

func (ContainerVolume) TableName() string {
	return "container_volumes"
}

func (ContainerRegistry) TableName() string {
	return "container_registries"
}

func (ContainerLog) TableName() string {
	return "container_logs"
}
