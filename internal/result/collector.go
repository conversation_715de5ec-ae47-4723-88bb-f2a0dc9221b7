package result

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"paas-platform/pkg/logger"
	"paas-platform/pkg/storage"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ResultCollector 结果收集器接口
type ResultCollector interface {
	// 收集执行结果
	CollectResult(ctx context.Context, taskID string, result *ExecutionResult) error
	
	// 收集输出文件
	CollectArtifacts(ctx context.Context, taskID string, outputPath string) ([]Artifact, error)
	
	// 查询执行历史
	QueryExecutionHistory(ctx context.Context, filter *HistoryFilter) ([]*ExecutionRecord, int64, error)
	
	// 获取执行结果
	GetExecutionResult(ctx context.Context, taskID string) (*ExecutionRecord, error)
	
	// 获取产物列表
	GetArtifacts(ctx context.Context, taskID string) ([]Artifact, error)
	
	// 下载产物
	DownloadArtifact(ctx context.Context, taskID, artifactName string) (io.ReadCloser, error)
	
	// 清理过期结果
	CleanupExpiredResults(ctx context.Context, retentionDays int) error
	
	// 导出执行记录
	ExportExecutionRecords(ctx context.Context, filter *ExportFilter) (string, error)
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	TaskID      string                 `json:"task_id"`
	Status      string                 `json:"status"`
	ExitCode    int                    `json:"exit_code"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    int64                  `json:"duration"` // 毫秒
	Resources   ResourceUsage          `json:"resources"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU使用率 (%)
	MemoryUsage int64   `json:"memory_usage"` // 内存使用量 (字节)
	DiskUsage   int64   `json:"disk_usage"`   // 磁盘使用量 (字节)
	NetworkRx   int64   `json:"network_rx"`   // 网络接收字节
	NetworkTx   int64   `json:"network_tx"`   // 网络发送字节
}

// Artifact 执行产物
type Artifact struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
	URL      string `json:"url"`
	Hash     string `json:"hash"`
}

// HistoryFilter 历史查询过滤器
type HistoryFilter struct {
	TaskID    string    `json:"task_id"`
	AppID     string    `json:"app_id"`
	UserID    string    `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	Status    string    `json:"status"`
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
	Page      int       `json:"page"`
	PageSize  int       `json:"page_size"`
}

// ExportFilter 导出过滤器
type ExportFilter struct {
	HistoryFilter
	Format string `json:"format"` // csv, json, xlsx
}

// resultCollector 结果收集器实现
type resultCollector struct {
	db      *gorm.DB
	storage storage.Storage
	logger  logger.Logger
	config  CollectorConfig
}

// CollectorConfig 收集器配置
type CollectorConfig struct {
	ArtifactBasePath   string        `json:"artifact_base_path"`
	MaxArtifactSize    int64         `json:"max_artifact_size"`
	AllowedMimeTypes   []string      `json:"allowed_mime_types"`
	RetentionDays      int           `json:"retention_days"`
	CompressionEnabled bool          `json:"compression_enabled"`
	EncryptionEnabled  bool          `json:"encryption_enabled"`
	CleanupInterval    time.Duration `json:"cleanup_interval"`
}

// NewResultCollector 创建结果收集器
func NewResultCollector(
	db *gorm.DB,
	storage storage.Storage,
	logger logger.Logger,
	config CollectorConfig,
) ResultCollector {
	collector := &resultCollector{
		db:      db,
		storage: storage,
		logger:  logger,
		config:  config,
	}

	// 启动清理协程
	go collector.startCleanupRoutine()

	return collector
}

// CollectResult 收集执行结果
func (c *resultCollector) CollectResult(ctx context.Context, taskID string, result *ExecutionResult) error {
	// 创建执行记录
	record := &ExecutionRecord{
		ID          uuid.New().String(),
		TaskID      taskID,
		Status      result.Status,
		ExitCode    result.ExitCode,
		Output:      result.Output,
		Error:       result.Error,
		StartTime   result.StartTime,
		EndTime     result.EndTime,
		Duration:    result.Duration,
		CreatedAt   time.Now(),
	}

	// 序列化资源使用情况
	if resourceBytes, err := json.Marshal(result.Resources); err == nil {
		record.Resources = resourceBytes
	}

	// 序列化元数据
	if metadataBytes, err := json.Marshal(result.Metadata); err == nil {
		record.Metadata = metadataBytes
	}

	// 保存执行记录
	if err := c.db.Create(record).Error; err != nil {
		return fmt.Errorf("保存执行记录失败: %w", err)
	}

	c.logger.Info("执行结果收集完成", "task_id", taskID, "status", result.Status)
	return nil
}

// CollectArtifacts 收集输出文件
func (c *resultCollector) CollectArtifacts(ctx context.Context, taskID string, outputPath string) ([]Artifact, error) {
	var artifacts []Artifact

	// 检查输出目录是否存在
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		c.logger.Debug("输出目录不存在", "path", outputPath)
		return artifacts, nil
	}

	// 遍历输出目录
	err := filepath.Walk(outputPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件大小限制
		if info.Size() > c.config.MaxArtifactSize {
			c.logger.Warn("文件超过大小限制，跳过", "file", path, "size", info.Size())
			return nil
		}

		// 获取相对路径
		relPath, err := filepath.Rel(outputPath, path)
		if err != nil {
			return err
		}

		// 检测MIME类型
		mimeType, err := c.detectMimeType(path)
		if err != nil {
			c.logger.Error("检测MIME类型失败", "error", err, "file", path)
			mimeType = "application/octet-stream"
		}

		// 检查允许的MIME类型
		if !c.isAllowedMimeType(mimeType) {
			c.logger.Warn("不允许的文件类型，跳过", "file", path, "mime_type", mimeType)
			return nil
		}

		// 计算文件哈希
		hash, err := c.calculateFileHash(path)
		if err != nil {
			c.logger.Error("计算文件哈希失败", "error", err, "file", path)
			hash = ""
		}

		// 上传文件到存储服务
		storageKey := fmt.Sprintf("artifacts/%s/%s", taskID, relPath)
		url, err := c.uploadFile(ctx, path, storageKey)
		if err != nil {
			c.logger.Error("上传文件失败", "error", err, "file", path)
			return err
		}

		// 创建产物记录
		artifact := Artifact{
			Name:     filepath.Base(path),
			Path:     relPath,
			Size:     info.Size(),
			MimeType: mimeType,
			URL:      url,
			Hash:     hash,
		}

		artifacts = append(artifacts, artifact)

		// 保存产物记录到数据库
		artifactRecord := &ArtifactRecord{
			ID:       uuid.New().String(),
			TaskID:   taskID,
			Name:     artifact.Name,
			Path:     artifact.Path,
			Size:     artifact.Size,
			MimeType: artifact.MimeType,
			URL:      artifact.URL,
			Hash:     artifact.Hash,
			CreatedAt: time.Now(),
		}

		if err := c.db.Create(artifactRecord).Error; err != nil {
			c.logger.Error("保存产物记录失败", "error", err, "artifact", artifact.Name)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("收集产物失败: %w", err)
	}

	c.logger.Info("产物收集完成", "task_id", taskID, "count", len(artifacts))
	return artifacts, nil
}

// QueryExecutionHistory 查询执行历史
func (c *resultCollector) QueryExecutionHistory(ctx context.Context, filter *HistoryFilter) ([]*ExecutionRecord, int64, error) {
	query := c.db.Model(&ExecutionRecord{})

	// 应用过滤条件
	if filter.TaskID != "" {
		query = query.Where("task_id = ?", filter.TaskID)
	}
	if filter.AppID != "" {
		query = query.Where("app_id = ?", filter.AppID)
	}
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.TenantID != "" {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.StartDate != nil {
		query = query.Where("start_time >= ?", filter.StartDate)
	}
	if filter.EndDate != nil {
		query = query.Where("end_time <= ?", filter.EndDate)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取记录总数失败: %w", err)
	}

	// 分页查询
	var records []*ExecutionRecord
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(filter.PageSize).Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("查询执行历史失败: %w", err)
	}

	return records, total, nil
}

// GetExecutionResult 获取执行结果
func (c *resultCollector) GetExecutionResult(ctx context.Context, taskID string) (*ExecutionRecord, error) {
	var record ExecutionRecord
	if err := c.db.Where("task_id = ?", taskID).First(&record).Error; err != nil {
		return nil, fmt.Errorf("执行记录不存在: %w", err)
	}

	return &record, nil
}

// GetArtifacts 获取产物列表
func (c *resultCollector) GetArtifacts(ctx context.Context, taskID string) ([]Artifact, error) {
	var records []ArtifactRecord
	if err := c.db.Where("task_id = ?", taskID).Find(&records).Error; err != nil {
		return nil, fmt.Errorf("查询产物记录失败: %w", err)
	}

	var artifacts []Artifact
	for _, record := range records {
		artifacts = append(artifacts, Artifact{
			Name:     record.Name,
			Path:     record.Path,
			Size:     record.Size,
			MimeType: record.MimeType,
			URL:      record.URL,
			Hash:     record.Hash,
		})
	}

	return artifacts, nil
}

// DownloadArtifact 下载产物
func (c *resultCollector) DownloadArtifact(ctx context.Context, taskID, artifactName string) (io.ReadCloser, error) {
	// 查找产物记录
	var record ArtifactRecord
	if err := c.db.Where("task_id = ? AND name = ?", taskID, artifactName).First(&record).Error; err != nil {
		return nil, fmt.Errorf("产物不存在: %w", err)
	}

	// 从存储服务下载文件
	storageKey := fmt.Sprintf("artifacts/%s/%s", taskID, record.Path)
	reader, err := c.storage.Download(ctx, storageKey)
	if err != nil {
		return nil, fmt.Errorf("下载产物失败: %w", err)
	}

	return reader, nil
}

// CleanupExpiredResults 清理过期结果
func (c *resultCollector) CleanupExpiredResults(ctx context.Context, retentionDays int) error {
	expiredTime := time.Now().AddDate(0, 0, -retentionDays)

	// 查找过期记录
	var expiredRecords []ExecutionRecord
	if err := c.db.Where("created_at < ?", expiredTime).Find(&expiredRecords).Error; err != nil {
		return fmt.Errorf("查询过期记录失败: %w", err)
	}

	// 清理过期记录
	for _, record := range expiredRecords {
		// 删除产物文件
		if err := c.cleanupTaskArtifacts(ctx, record.TaskID); err != nil {
			c.logger.Error("清理任务产物失败", "error", err, "task_id", record.TaskID)
		}

		// 删除执行记录
		if err := c.db.Delete(&record).Error; err != nil {
			c.logger.Error("删除执行记录失败", "error", err, "record_id", record.ID)
		}
	}

	c.logger.Info("过期结果清理完成", "count", len(expiredRecords), "retention_days", retentionDays)
	return nil
}

// ExportExecutionRecords 导出执行记录
func (c *resultCollector) ExportExecutionRecords(ctx context.Context, filter *ExportFilter) (string, error) {
	// 查询执行记录
	records, _, err := c.QueryExecutionHistory(ctx, &filter.HistoryFilter)
	if err != nil {
		return "", fmt.Errorf("查询执行记录失败: %w", err)
	}

	// 根据格式导出
	switch strings.ToLower(filter.Format) {
	case "csv":
		return c.exportToCSV(ctx, records)
	case "json":
		return c.exportToJSON(ctx, records)
	case "xlsx":
		return c.exportToXLSX(ctx, records)
	default:
		return "", fmt.Errorf("不支持的导出格式: %s", filter.Format)
	}
}

// 私有方法

// detectMimeType 检测MIME类型
func (c *resultCollector) detectMimeType(filePath string) (string, error) {
	// TODO: 实现MIME类型检测
	// 可以使用 http.DetectContentType 或第三方库
	return "application/octet-stream", nil
}

// isAllowedMimeType 检查是否为允许的MIME类型
func (c *resultCollector) isAllowedMimeType(mimeType string) bool {
	if len(c.config.AllowedMimeTypes) == 0 {
		return true // 如果没有限制，允许所有类型
	}

	for _, allowed := range c.config.AllowedMimeTypes {
		if mimeType == allowed {
			return true
		}
	}
	return false
}

// calculateFileHash 计算文件哈希
func (c *resultCollector) calculateFileHash(filePath string) (string, error) {
	// TODO: 实现文件哈希计算 (SHA256)
	return "", nil
}

// uploadFile 上传文件到存储服务
func (c *resultCollector) uploadFile(ctx context.Context, filePath, storageKey string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 上传文件
	if err := c.storage.Upload(ctx, storageKey, file); err != nil {
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	// 生成访问URL
	url, err := c.storage.GetURL(ctx, storageKey)
	if err != nil {
		return "", fmt.Errorf("生成访问URL失败: %w", err)
	}

	return url, nil
}

// cleanupTaskArtifacts 清理任务产物
func (c *resultCollector) cleanupTaskArtifacts(ctx context.Context, taskID string) error {
	// 查找任务产物
	var artifacts []ArtifactRecord
	if err := c.db.Where("task_id = ?", taskID).Find(&artifacts).Error; err != nil {
		return fmt.Errorf("查询任务产物失败: %w", err)
	}

	// 删除存储文件
	for _, artifact := range artifacts {
		storageKey := fmt.Sprintf("artifacts/%s/%s", taskID, artifact.Path)
		if err := c.storage.Delete(ctx, storageKey); err != nil {
			c.logger.Error("删除存储文件失败", "error", err, "key", storageKey)
		}
	}

	// 删除产物记录
	if err := c.db.Where("task_id = ?", taskID).Delete(&ArtifactRecord{}).Error; err != nil {
		return fmt.Errorf("删除产物记录失败: %w", err)
	}

	return nil
}

// exportToCSV 导出为CSV格式
func (c *resultCollector) exportToCSV(ctx context.Context, records []*ExecutionRecord) (string, error) {
	// TODO: 实现CSV导出
	return "", fmt.Errorf("CSV导出功能开发中")
}

// exportToJSON 导出为JSON格式
func (c *resultCollector) exportToJSON(ctx context.Context, records []*ExecutionRecord) (string, error) {
	// TODO: 实现JSON导出
	return "", fmt.Errorf("JSON导出功能开发中")
}

// exportToXLSX 导出为XLSX格式
func (c *resultCollector) exportToXLSX(ctx context.Context, records []*ExecutionRecord) (string, error) {
	// TODO: 实现XLSX导出
	return "", fmt.Errorf("XLSX导出功能开发中")
}

// startCleanupRoutine 启动清理协程
func (c *resultCollector) startCleanupRoutine() {
	ticker := time.NewTicker(c.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		ctx := context.Background()
		if err := c.CleanupExpiredResults(ctx, c.config.RetentionDays); err != nil {
			c.logger.Error("定时清理过期结果失败", "error", err)
		}
	}
}
