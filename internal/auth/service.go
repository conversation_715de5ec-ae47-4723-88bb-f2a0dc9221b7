package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Service 认证服务接口
type Service interface {
	// 用户认证
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	Logout(ctx context.Context, userID string, sessionID string) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenPair, error)
	
	// 用户管理
	CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
	GetUser(ctx context.Context, userID string) (*User, error)
	GetUserByUsername(ctx context.Context, username string, tenantID string) (*User, error)
	ListUsers(ctx context.Context, tenantID string, filter *UserFilter) ([]*User, int64, error)
	UpdateUser(ctx context.Context, userID string, req *UpdateUserRequest) (*User, error)
	DeleteUser(ctx context.Context, userID string) error
	ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error
	
	// 租户管理
	CreateTenant(ctx context.Context, req *CreateTenantRequest) (*Tenant, error)
	GetTenant(ctx context.Context, tenantID string) (*Tenant, error)
	ListTenants(ctx context.Context, filter *TenantFilter) ([]*Tenant, int64, error)
	UpdateTenant(ctx context.Context, tenantID string, req *UpdateTenantRequest) (*Tenant, error)
	
	// 角色管理
	CreateRole(ctx context.Context, req *CreateRoleRequest) (*Role, error)
	GetRole(ctx context.Context, roleID string) (*Role, error)
	ListRoles(ctx context.Context, tenantID string) ([]*Role, error)
	UpdateRole(ctx context.Context, roleID string, req *UpdateRoleRequest) (*Role, error)
	DeleteRole(ctx context.Context, roleID string) error
	
	// 用户角色管理
	AssignRole(ctx context.Context, userID string, roleID string) error
	UnassignRole(ctx context.Context, userID string, roleID string) error
	GetUserRoles(ctx context.Context, userID string) ([]*Role, error)
	GetUserPermissions(ctx context.Context, userID string) ([]string, error)
	
	// 权限检查
	HasPermission(ctx context.Context, userID string, permission string) (bool, error)
	CheckPermission(ctx context.Context, userID string, resource string, action string, scope string) (bool, error)
	
	// 会话管理
	CreateSession(ctx context.Context, userID string, ipAddress string, userAgent string) (*Session, error)
	GetSession(ctx context.Context, sessionID string) (*Session, error)
	UpdateSessionActivity(ctx context.Context, sessionID string) error
	DeleteSession(ctx context.Context, sessionID string) error
	DeleteUserSessions(ctx context.Context, userID string) error
	
	// 审计日志
	LogAuditEvent(ctx context.Context, event *AuditEvent) error
	GetAuditLogs(ctx context.Context, filter *AuditFilter) ([]*AuditLog, int64, error)
}

// AuthService 认证服务实现
type AuthService struct {
	db          *gorm.DB
	jwtService  JWTService
	logger      Logger
	config      AuthConfig
}

// Logger 日志接口
type Logger interface {
	Info(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Debug(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret           string        `json:"jwt_secret"`
	AccessTokenExpiry   time.Duration `json:"access_token_expiry"`
	RefreshTokenExpiry  time.Duration `json:"refresh_token_expiry"`
	SessionExpiry       time.Duration `json:"session_expiry"`
	MaxLoginAttempts    int           `json:"max_login_attempts"`
	LockoutDuration     time.Duration `json:"lockout_duration"`
	PasswordCost        int           `json:"password_cost"`
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, jwtService JWTService, logger Logger, config AuthConfig) Service {
	return &AuthService{
		db:         db,
		jwtService: jwtService,
		logger:     logger,
		config:     config,
	}
}

// Login 用户登录
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 记录登录尝试
	attempt := &LoginAttempt{
		ID:        uuid.New().String(),
		Username:  req.Username,
		TenantID:  req.TenantID,
		IPAddress: req.IPAddress,
		UserAgent: req.UserAgent,
		Success:   false,
		Timestamp: time.Now(),
	}
	
	// 检查登录尝试次数
	if err := s.checkLoginAttempts(ctx, req.Username, req.TenantID, req.IPAddress); err != nil {
		attempt.ErrorMsg = err.Error()
		s.db.Create(attempt)
		return nil, err
	}
	
	// 获取用户信息
	user, err := s.GetUserByUsername(ctx, req.Username, req.TenantID)
	if err != nil {
		attempt.ErrorMsg = "用户不存在"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 检查用户状态
	if user.Status != UserStatusActive {
		attempt.ErrorMsg = fmt.Sprintf("用户状态异常: %s", user.Status)
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	// 检查用户是否被锁定
	if user.LockedUntil != nil && time.Now().Before(*user.LockedUntil) {
		attempt.ErrorMsg = "用户账户已被锁定"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户账户已被锁定，请稍后再试")
	}
	
	// 验证密码
	if !s.verifyPassword(req.Password, user.PasswordHash) {
		// 增加失败登录次数
		s.incrementFailedLogins(ctx, user.ID)
		
		attempt.ErrorMsg = "密码错误"
		s.db.Create(attempt)
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 登录成功
	attempt.Success = true
	s.db.Create(attempt)
	
	// 重置失败登录次数
	s.resetFailedLogins(ctx, user.ID)
	
	// 更新最后登录时间
	s.updateLastLogin(ctx, user.ID)
	
	// 生成令牌对
	tokenPair, err := s.jwtService.GenerateTokenPair(user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}
	
	// 创建会话
	session, err := s.CreateSession(ctx, user.ID, req.IPAddress, req.UserAgent)
	if err != nil {
		s.logger.Error("创建会话失败", "error", err, "user_id", user.ID)
	}
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "user_login",
		Result:    "success",
		UserID:    user.ID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		IPAddress: req.IPAddress,
		UserAgent: req.UserAgent,
		Resource:  "auth/login",
		Details: map[string]interface{}{
			"login_method": "password",
			"session_id":   session.ID,
		},
	})
	
	response := &LoginResponse{
		User:         user,
		TokenPair:    tokenPair,
		SessionID:    session.ID,
		ExpiresIn:    int(s.config.AccessTokenExpiry.Seconds()),
		Permissions:  []string{}, // TODO: 获取用户权限
	}
	
	s.logger.Info("用户登录成功", "user_id", user.ID, "username", user.Username, "tenant_id", user.TenantID)
	return response, nil
}

// CreateUser 创建用户
func (s *AuthService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}
	
	// 检查用户名是否已存在
	existingUser, err := s.GetUserByUsername(ctx, req.Username, req.TenantID)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("用户名 '%s' 已存在", req.Username)
	}
	
	// 检查邮箱是否已存在
	var emailUser User
	err = s.db.Where("email = ? AND tenant_id = ?", req.Email, req.TenantID).First(&emailUser).Error
	if err == nil {
		return nil, fmt.Errorf("邮箱 '%s' 已被使用", req.Email)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	
	// 验证密码强度
	if err := s.validatePassword(req.Password); err != nil {
		return nil, fmt.Errorf("密码强度不足: %w", err)
	}
	
	// 哈希密码
	passwordHash, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}
	
	// 创建用户
	user := &User{
		ID:           uuid.New().String(),
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: passwordHash,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Phone:        req.Phone,
		Status:       UserStatusActive,
		TenantID:     req.TenantID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	// 在事务中创建用户和分配默认角色
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		if err := tx.Create(user).Error; err != nil {
			return fmt.Errorf("创建用户失败: %w", err)
		}
		
		// 分配默认角色
		if req.RoleIDs != nil && len(req.RoleIDs) > 0 {
			for _, roleID := range req.RoleIDs {
				userRole := &UserRole{
					UserID:    user.ID,
					RoleID:    roleID,
					CreatedAt: time.Now(),
					CreatedBy: req.CreatedBy,
				}
				if err := tx.Create(userRole).Error; err != nil {
					return fmt.Errorf("分配角色失败: %w", err)
				}
			}
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "management",
		EventName: "user_created",
		Result:    "success",
		UserID:    req.CreatedBy,
		TenantID:  req.TenantID,
		Resource:  fmt.Sprintf("user/%s", user.ID),
		Details: map[string]interface{}{
			"created_user_id": user.ID,
			"username":        user.Username,
			"email":           user.Email,
		},
	})
	
	s.logger.Info("用户创建成功", "user_id", user.ID, "username", user.Username, "tenant_id", req.TenantID)
	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *AuthService) GetUserByUsername(ctx context.Context, username string, tenantID string) (*User, error) {
	var user User
	err := s.db.Preload("UserRoles.Role").Where("username = ? AND tenant_id = ?", username, tenantID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在: %s", username)
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error {
	// 获取用户信息
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return err
	}
	
	// 验证当前密码
	if !s.verifyPassword(req.CurrentPassword, user.PasswordHash) {
		return fmt.Errorf("当前密码错误")
	}
	
	// 验证新密码强度
	if err := s.validatePassword(req.NewPassword); err != nil {
		return fmt.Errorf("新密码强度不足: %w", err)
	}
	
	// 检查密码历史
	if err := s.checkPasswordHistory(ctx, userID, req.NewPassword); err != nil {
		return err
	}
	
	// 哈希新密码
	newPasswordHash, err := s.hashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}
	
	// 在事务中更新密码和记录历史
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 保存旧密码到历史记录
		history := &PasswordHistory{
			ID:           uuid.New().String(),
			UserID:       userID,
			PasswordHash: user.PasswordHash,
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(history).Error; err != nil {
			return fmt.Errorf("保存密码历史失败: %w", err)
		}
		
		// 更新用户密码
		if err := tx.Model(user).Update("password_hash", newPasswordHash).Error; err != nil {
			return fmt.Errorf("更新密码失败: %w", err)
		}
		
		// 清理过期的密码历史
		s.cleanupPasswordHistory(tx, userID)
		
		return nil
	})
	
	if err != nil {
		return err
	}
	
	// 撤销所有会话 (强制重新登录)
	s.DeleteUserSessions(ctx, userID)
	
	// 记录审计日志
	s.LogAuditEvent(ctx, &AuditEvent{
		EventType: "authentication",
		EventName: "password_changed",
		Result:    "success",
		UserID:    userID,
		TenantID:  user.TenantID,
		Username:  user.Username,
		Resource:  fmt.Sprintf("user/%s/password", userID),
	})
	
	s.logger.Info("用户密码修改成功", "user_id", userID)
	return nil
}

// HasPermission 检查用户是否有指定权限
func (s *AuthService) HasPermission(ctx context.Context, userID string, permission string) (bool, error) {
	permissions, err := s.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}
	
	// 检查是否有通配符权限
	for _, perm := range permissions {
		if perm == "*:*" || perm == permission {
			return true, nil
		}
		
		// 检查资源级通配符
		if strings.HasSuffix(perm, ":*") {
			resource := strings.TrimSuffix(perm, ":*")
			if strings.HasPrefix(permission, resource+":") {
				return true, nil
			}
		}
	}
	
	return false, nil
}

// GetUserPermissions 获取用户权限
func (s *AuthService) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	var permissions []string
	
	// 获取用户角色
	roles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	// 合并所有角色的权限
	permissionSet := make(map[string]bool)
	for _, role := range roles {
		for _, perm := range role.Permissions {
			permissionSet[perm] = true
		}
	}
	
	// 转换为切片
	for perm := range permissionSet {
		permissions = append(permissions, perm)
	}
	
	return permissions, nil
}

// CreateSession 创建会话
func (s *AuthService) CreateSession(ctx context.Context, userID string, ipAddress string, userAgent string) (*Session, error) {
	session := &Session{
		ID:           uuid.New().String(),
		UserID:       userID,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
		ExpiresAt:    time.Now().Add(s.config.SessionExpiry),
		Data:         make(map[string]interface{}),
	}
	
	// 获取用户信息设置租户ID
	user, err := s.GetUser(ctx, userID)
	if err != nil {
		return nil, err
	}
	session.TenantID = user.TenantID
	
	if err := s.db.Create(session).Error; err != nil {
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}
	
	return session, nil
}

// 辅助方法

// hashPassword 哈希密码
func (s *AuthService) hashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), s.config.PasswordCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// verifyPassword 验证密码
func (s *AuthService) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// validatePassword 验证密码强度
func (s *AuthService) validatePassword(password string) error {
	// TODO: 实现密码强度验证逻辑
	if len(password) < 8 {
		return fmt.Errorf("密码长度至少8位")
	}
	return nil
}

// checkLoginAttempts 检查登录尝试次数
func (s *AuthService) checkLoginAttempts(ctx context.Context, username, tenantID, ipAddress string) error {
	// 检查最近一段时间的失败登录次数
	var failedCount int64
	since := time.Now().Add(-s.config.LockoutDuration)
	
	err := s.db.Model(&LoginAttempt{}).
		Where("username = ? AND tenant_id = ? AND ip_address = ? AND success = false AND timestamp > ?",
			username, tenantID, ipAddress, since).
		Count(&failedCount).Error
	if err != nil {
		return fmt.Errorf("检查登录尝试失败: %w", err)
	}
	
	if failedCount >= int64(s.config.MaxLoginAttempts) {
		return fmt.Errorf("登录尝试次数过多，请稍后再试")
	}
	
	return nil
}

// incrementFailedLogins 增加失败登录次数
func (s *AuthService) incrementFailedLogins(ctx context.Context, userID string) {
	s.db.Model(&User{}).Where("id = ?", userID).UpdateColumn("failed_logins", gorm.Expr("failed_logins + 1"))
	
	// 检查是否需要锁定用户
	var user User
	if err := s.db.First(&user, "id = ?", userID).Error; err == nil {
		if user.FailedLogins >= s.config.MaxLoginAttempts {
			lockUntil := time.Now().Add(s.config.LockoutDuration)
			s.db.Model(&user).Update("locked_until", &lockUntil)
			s.logger.Warn("用户因多次登录失败被锁定", "user_id", userID, "failed_logins", user.FailedLogins)
		}
	}
}

// resetFailedLogins 重置失败登录次数
func (s *AuthService) resetFailedLogins(ctx context.Context, userID string) {
	s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"failed_logins": 0,
		"locked_until":  nil,
	})
}

// updateLastLogin 更新最后登录时间
func (s *AuthService) updateLastLogin(ctx context.Context, userID string) {
	now := time.Now()
	s.db.Model(&User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"last_login_at": &now,
		"login_count":   gorm.Expr("login_count + 1"),
	})
}

// checkPasswordHistory 检查密码历史
func (s *AuthService) checkPasswordHistory(ctx context.Context, userID string, newPassword string) error {
	// 获取密码历史
	var histories []PasswordHistory
	err := s.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(12).Find(&histories).Error
	if err != nil {
		return fmt.Errorf("获取密码历史失败: %w", err)
	}
	
	// 检查新密码是否与历史密码相同
	for _, history := range histories {
		if s.verifyPassword(newPassword, history.PasswordHash) {
			return fmt.Errorf("新密码不能与最近使用的密码相同")
		}
	}
	
	return nil
}

// cleanupPasswordHistory 清理密码历史
func (s *AuthService) cleanupPasswordHistory(tx *gorm.DB, userID string) {
	// 只保留最近12次密码历史
	tx.Where("user_id = ? AND id NOT IN (SELECT id FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 12)",
		userID, userID).Delete(&PasswordHistory{})
}
