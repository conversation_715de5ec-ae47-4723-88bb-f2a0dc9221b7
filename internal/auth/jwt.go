package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService JWT 服务接口
type JWTService interface {
	GenerateTokenPair(user *User) (*TokenPair, error)
	RefreshToken(refreshToken string) (*TokenPair, error)
	ValidateToken(token string) (*Claims, error)
	RevokeToken(tokenID string) error
	IsTokenRevoked(tokenID string) (bool, error)
}

// jwtService JWT 服务实现
type jwtService struct {
	secret             []byte
	accessTokenExpiry  time.Duration
	refreshTokenExpiry time.Duration
	authService        *AuthService
}

// Claims JWT 声明
type Claims struct {
	UserID      string   `json:"sub"`
	TenantID    string   `json:"tenant_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	SessionID   string   `json:"session_id"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

// NewJWTService 创建 JWT 服务
func NewJWTService(secret string, accessExpiry, refreshExpiry time.Duration) JWTService {
	return &jwtService{
		secret:             []byte(secret),
		accessTokenExpiry:  accessExpiry,
		refreshTokenExpiry: refreshExpiry,
	}
}

// SetAuthService 设置认证服务 (避免循环依赖)
func (j *jwtService) SetAuthService(authService *AuthService) {
	j.authService = authService
}

// GenerateTokenPair 生成令牌对
func (j *jwtService) GenerateTokenPair(user *User) (*TokenPair, error) {
	// 获取用户角色和权限
	var roles []string
	var permissions []string
	
	if j.authService != nil {
		userRoles, err := j.authService.GetUserRoles(context.Background(), user.ID)
		if err == nil {
			for _, role := range userRoles {
				roles = append(roles, role.Name)
				permissions = append(permissions, role.Permissions...)
			}
		}
	}
	
	// 生成访问令牌
	accessTokenID := uuid.New().String()
	accessClaims := &Claims{
		UserID:      user.ID,
		TenantID:    user.TenantID,
		Username:    user.Username,
		Email:       user.Email,
		Roles:       roles,
		Permissions: permissions,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        accessTokenID,
			Subject:   user.ID,
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.accessTokenExpiry)),
			Issuer:    "paas-platform",
			Audience:  []string{"paas-api"},
		},
	}
	
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}
	
	// 生成刷新令牌
	refreshTokenID := uuid.New().String()
	refreshClaims := &Claims{
		UserID:   user.ID,
		TenantID: user.TenantID,
		Username: user.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        refreshTokenID,
			Subject:   user.ID,
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.refreshTokenExpiry)),
			Issuer:    "paas-platform",
			Audience:  []string{"paas-refresh"},
		},
	}
	
	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}
	
	// 保存刷新令牌到数据库
	if j.authService != nil {
		refreshTokenRecord := &RefreshToken{
			ID:        refreshTokenID,
			UserID:    user.ID,
			Token:     refreshTokenString,
			ExpiresAt: time.Now().Add(j.refreshTokenExpiry),
			CreatedAt: time.Now(),
		}
		
		if err := j.authService.db.Create(refreshTokenRecord).Error; err != nil {
			return nil, fmt.Errorf("保存刷新令牌失败: %w", err)
		}
	}
	
	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		TokenType:    "Bearer",
		ExpiresIn:    int(j.accessTokenExpiry.Seconds()),
	}, nil
}

// ValidateToken 验证令牌
func (j *jwtService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("无效的签名方法: %v", token.Header["alg"])
		}
		return j.secret, nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("令牌解析失败: %w", err)
	}
	
	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("无效的令牌")
	}
	
	// 检查令牌是否被撤销
	if revoked, err := j.IsTokenRevoked(claims.ID); err != nil {
		return nil, fmt.Errorf("检查令牌状态失败: %w", err)
	} else if revoked {
		return nil, fmt.Errorf("令牌已被撤销")
	}
	
	return claims, nil
}

// RefreshToken 刷新令牌
func (j *jwtService) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	// 验证刷新令牌
	claims, err := j.ValidateToken(refreshTokenString)
	if err != nil {
		return nil, fmt.Errorf("刷新令牌无效: %w", err)
	}
	
	// 检查令牌用途 (必须是刷新令牌)
	if len(claims.Audience) == 0 || claims.Audience[0] != "paas-refresh" {
		return nil, fmt.Errorf("令牌类型错误")
	}
	
	// 检查刷新令牌是否存在且未撤销
	if j.authService != nil {
		var refreshToken RefreshToken
		err := j.authService.db.Where("id = ? AND revoked = false", claims.ID).First(&refreshToken).Error
		if err != nil {
			return nil, fmt.Errorf("刷新令牌不存在或已撤销")
		}
		
		// 撤销旧的刷新令牌
		now := time.Now()
		j.authService.db.Model(&refreshToken).Updates(map[string]interface{}{
			"revoked":    true,
			"revoked_at": &now,
		})
	}
	
	// 获取用户信息
	if j.authService == nil {
		return nil, fmt.Errorf("认证服务未初始化")
	}
	
	user, err := j.authService.GetUser(context.Background(), claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}
	
	// 检查用户状态
	if user.Status != UserStatusActive {
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	// 生成新的令牌对
	return j.GenerateTokenPair(user)
}

// RevokeToken 撤销令牌
func (j *jwtService) RevokeToken(tokenID string) error {
	if j.authService == nil {
		return fmt.Errorf("认证服务未初始化")
	}
	
	// 添加到黑名单
	blacklist := &JWTBlacklist{
		JTI:       tokenID,
		RevokedAt: time.Now(),
		Reason:    "manual_revoke",
	}
	
	// 获取令牌信息
	claims, err := j.ValidateToken(tokenID)
	if err == nil {
		blacklist.UserID = claims.UserID
		blacklist.ExpiresAt = claims.ExpiresAt.Time
	}
	
	if err := j.authService.db.Create(blacklist).Error; err != nil {
		return fmt.Errorf("撤销令牌失败: %w", err)
	}
	
	return nil
}

// IsTokenRevoked 检查令牌是否被撤销
func (j *jwtService) IsTokenRevoked(tokenID string) (bool, error) {
	if j.authService == nil {
		return false, nil
	}
	
	var count int64
	err := j.authService.db.Model(&JWTBlacklist{}).Where("jti = ?", tokenID).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查令牌黑名单失败: %w", err)
	}
	
	return count > 0, nil
}

// CleanupExpiredTokens 清理过期令牌
func (j *jwtService) CleanupExpiredTokens() error {
	if j.authService == nil {
		return fmt.Errorf("认证服务未初始化")
	}
	
	now := time.Now()
	
	// 清理过期的刷新令牌
	if err := j.authService.db.Where("expires_at < ?", now).Delete(&RefreshToken{}).Error; err != nil {
		return fmt.Errorf("清理过期刷新令牌失败: %w", err)
	}
	
	// 清理过期的黑名单记录
	if err := j.authService.db.Where("expires_at < ?", now).Delete(&JWTBlacklist{}).Error; err != nil {
		return fmt.Errorf("清理过期黑名单记录失败: %w", err)
	}
	
	// 清理过期的密码重置令牌
	if err := j.authService.db.Where("expires_at < ?", now).Delete(&PasswordResetToken{}).Error; err != nil {
		return fmt.Errorf("清理过期密码重置令牌失败: %w", err)
	}
	
	// 清理过期的会话
	if err := j.authService.db.Where("expires_at < ?", now).Delete(&Session{}).Error; err != nil {
		return fmt.Errorf("清理过期会话失败: %w", err)
	}
	
	return nil
}
