package auth

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// Handler 认证 HTTP 处理器
type Handler struct {
	service Service
	logger  Logger
}

// NewHandler 创建认证处理器
func NewHandler(service Service, logger Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	// 认证相关路由 (无需认证)
	auth := router.Group("/auth")
	{
		auth.POST("/login", h.Login)                    // 用户登录
		auth.POST("/logout", h.Logout)                  // 用户登出
		auth.POST("/refresh", h.RefreshToken)           // 刷新令牌
		auth.POST("/reset-password", h.ResetPassword)   // 重置密码
		auth.POST("/confirm-reset", h.ConfirmResetPassword) // 确认重置密码
	}
	
	// 用户管理路由 (需要认证)
	users := router.Group("/users")
	{
		users.POST("", h.CreateUser)                    // 创建用户
		users.GET("", h.ListUsers)                      // 获取用户列表
		users.GET("/:id", h.GetUser)                    // 获取用户详情
		users.PUT("/:id", h.UpdateUser)                 // 更新用户
		users.DELETE("/:id", h.DeleteUser)              // 删除用户
		users.POST("/:id/change-password", h.ChangePassword) // 修改密码
		users.GET("/:id/roles", h.GetUserRoles)         // 获取用户角色
		users.POST("/:id/roles", h.AssignRole)          // 分配角色
		users.DELETE("/:id/roles/:role_id", h.UnassignRole) // 取消角色
		users.GET("/:id/permissions", h.GetUserPermissions) // 获取用户权限
		users.GET("/:id/sessions", h.GetUserSessions)   // 获取用户会话
		users.DELETE("/:id/sessions", h.DeleteUserSessions) // 删除用户会话
	}
	
	// 租户管理路由
	tenants := router.Group("/tenants")
	{
		tenants.POST("", h.CreateTenant)                // 创建租户
		tenants.GET("", h.ListTenants)                  // 获取租户列表
		tenants.GET("/:id", h.GetTenant)                // 获取租户详情
		tenants.PUT("/:id", h.UpdateTenant)             // 更新租户
		tenants.GET("/:id/users", h.GetTenantUsers)     // 获取租户用户
		tenants.GET("/:id/stats", h.GetTenantStats)     // 获取租户统计
	}
	
	// 角色管理路由
	roles := router.Group("/roles")
	{
		roles.POST("", h.CreateRole)                    // 创建角色
		roles.GET("", h.ListRoles)                      // 获取角色列表
		roles.GET("/:id", h.GetRole)                    // 获取角色详情
		roles.PUT("/:id", h.UpdateRole)                 // 更新角色
		roles.DELETE("/:id", h.DeleteRole)              // 删除角色
		roles.GET("/:id/users", h.GetRoleUsers)         // 获取角色用户
	}
	
	// 权限管理路由
	permissions := router.Group("/permissions")
	{
		permissions.GET("", h.ListPermissions)          // 获取权限列表
		permissions.POST("/check", h.CheckPermission)   // 检查权限
	}
	
	// 会话管理路由
	sessions := router.Group("/sessions")
	{
		sessions.GET("", h.ListSessions)                // 获取会话列表
		sessions.GET("/:id", h.GetSession)              // 获取会话详情
		sessions.DELETE("/:id", h.DeleteSession)        // 删除会话
	}
	
	// 审计日志路由
	audit := router.Group("/audit")
	{
		audit.GET("/logs", h.GetAuditLogs)              // 获取审计日志
		audit.GET("/stats", h.GetAuditStats)           // 获取审计统计
		audit.GET("/security-report", h.GetSecurityReport) // 获取安全报告
	}
	
	// MFA 管理路由
	mfa := router.Group("/mfa")
	{
		mfa.POST("/setup", h.SetupMFA)                  // 设置 MFA
		mfa.POST("/verify", h.VerifyMFA)                // 验证 MFA
		mfa.GET("/devices", h.ListMFADevices)           // 获取 MFA 设备
		mfa.DELETE("/devices/:id", h.DeleteMFADevice)   // 删除 MFA 设备
	}
	
	// API 密钥管理路由
	apiKeys := router.Group("/api-keys")
	{
		apiKeys.POST("", h.CreateAPIKey)                // 创建 API 密钥
		apiKeys.GET("", h.ListAPIKeys)                  // 获取 API 密钥列表
		apiKeys.DELETE("/:id", h.DeleteAPIKey)          // 删除 API 密钥
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户使用用户名和密码登录系统
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录请求"
// @Success 200 {object} LoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定登录请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置客户端信息
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")
	
	response, err := h.service.Login(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("用户登录失败", "error", err, "username", req.Username, "tenant_id", req.TenantID)
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "LOGIN_FAILED",
			Message:   "登录失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置刷新令牌到 Cookie (HttpOnly, Secure)
	c.SetCookie(
		"refresh_token",
		response.TokenPair.RefreshToken,
		int(time.Hour*24*7/time.Second), // 7天
		"/",
		"",
		true,  // Secure
		true,  // HttpOnly
	)
	
	h.logger.Info("用户登录成功", "user_id", response.User.ID, "username", response.User.Username)
	c.JSON(http.StatusOK, response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出系统，撤销令牌和会话
// @Tags 认证
// @Produce json
// @Success 200 {object} SuccessResponse "登出成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/logout [post]
func (h *Handler) Logout(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	sessionID, _ := c.Get("session_id")
	sessionIDStr := ""
	if sessionID != nil {
		sessionIDStr = sessionID.(string)
	}
	
	err := h.service.Logout(c.Request.Context(), userID.(string), sessionIDStr)
	if err != nil {
		h.logger.Error("用户登出失败", "error", err, "user_id", userID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "LOGOUT_FAILED",
			Message:   "登出失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 清除刷新令牌 Cookie
	c.SetCookie("refresh_token", "", -1, "/", "", true, true)
	
	h.logger.Info("用户登出成功", "user_id", userID)
	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "登出成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body CreateUserRequest true "创建用户请求"
// @Success 201 {object} User "创建成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 409 {object} ErrorResponse "用户已存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [post]
func (h *Handler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定创建用户请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 设置创建者
	createdBy, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	req.CreatedBy = createdBy.(string)
	
	// 设置租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	req.TenantID = tenantID.(string)
	
	user, err := h.service.CreateUser(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建用户失败", "error", err, "username", req.Username)
		
		statusCode := http.StatusInternalServerError
		errorCode := "CREATE_USER_FAILED"
		
		// 根据错误类型设置不同的状态码
		if contains([]string{"用户名", "邮箱"}, err.Error()) {
			statusCode = http.StatusConflict
			errorCode = "USER_ALREADY_EXISTS"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "创建用户失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	h.logger.Info("用户创建成功", "user_id", user.ID, "username", user.Username)
	c.JSON(http.StatusCreated, user)
}

// GetUser 获取用户详情
// @Summary 获取用户详情
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} UserResponse "获取成功"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [get]
func (h *Handler) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	user, err := h.service.GetUser(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户失败", "error", err, "user_id", userID)
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:      "USER_NOT_FOUND",
			Message:   "用户不存在",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 获取用户角色和权限
	roles, _ := h.service.GetUserRoles(c.Request.Context(), userID)
	permissions, _ := h.service.GetUserPermissions(c.Request.Context(), userID)
	
	response := &UserResponse{
		User:        user,
		Roles:       roles,
		Permissions: permissions,
		LastLogin:   user.LastLoginAt,
	}
	
	c.JSON(http.StatusOK, response)
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取租户内的用户列表
// @Tags 用户管理
// @Produce json
// @Param username query string false "用户名过滤"
// @Param email query string false "邮箱过滤"
// @Param status query string false "状态过滤"
// @Param role_id query string false "角色过滤"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} UserListResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [get]
func (h *Handler) ListUsers(c *gin.Context) {
	// 获取租户ID
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 解析查询参数
	filter := &UserFilter{
		Username: c.Query("username"),
		Email:    c.Query("email"),
		RoleID:   c.Query("role_id"),
		Page:     1,
		PageSize: 20,
	}
	
	if status := c.Query("status"); status != "" {
		filter.Status = UserStatus(status)
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}
	
	users, total, err := h.service.ListUsers(c.Request.Context(), tenantID.(string), filter)
	if err != nil {
		h.logger.Error("获取用户列表失败", "error", err, "tenant_id", tenantID)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "LIST_USERS_FAILED",
			Message:   "获取用户列表失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 转换为响应格式
	userResponses := make([]*UserResponse, len(users))
	for i, user := range users {
		roles, _ := h.service.GetUserRoles(c.Request.Context(), user.ID)
		permissions, _ := h.service.GetUserPermissions(c.Request.Context(), user.ID)
		
		userResponses[i] = &UserResponse{
			User:        user,
			Roles:       roles,
			Permissions: permissions,
			LastLogin:   user.LastLoginAt,
		}
	}
	
	response := &UserListResponse{
		Users: userResponses,
		Total: total,
		Page:  filter.Page,
		Size:  len(userResponses),
	}
	
	c.JSON(http.StatusOK, response)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 用户修改自己的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param request body ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "当前密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id}/change-password [post]
func (h *Handler) ChangePassword(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_USER_ID",
			Message:   "用户ID不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 检查权限 (只能修改自己的密码或管理员权限)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "UNAUTHORIZED",
			Message:   "未授权访问",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	if currentUserID.(string) != userID {
		// TODO: 检查是否有管理员权限
		c.JSON(http.StatusForbidden, ErrorResponse{
			Code:      "FORBIDDEN",
			Message:   "权限不足",
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定修改密码请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	err := h.service.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		h.logger.Error("修改密码失败", "error", err, "user_id", userID)
		
		statusCode := http.StatusInternalServerError
		errorCode := "CHANGE_PASSWORD_FAILED"
		
		// 根据错误类型设置不同的状态码
		if strings.Contains(err.Error(), "当前密码错误") {
			statusCode = http.StatusUnauthorized
			errorCode = "INVALID_CURRENT_PASSWORD"
		} else if strings.Contains(err.Error(), "密码强度") {
			statusCode = http.StatusBadRequest
			errorCode = "WEAK_PASSWORD"
		}
		
		c.JSON(statusCode, ErrorResponse{
			Code:      errorCode,
			Message:   "修改密码失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	h.logger.Info("密码修改成功", "user_id", userID)
	c.JSON(http.StatusOK, SuccessResponse{
		Message:   "密码修改成功",
		Timestamp: time.Now().Format(time.RFC3339),
	})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Produce json
// @Success 200 {object} TokenPair "刷新成功"
// @Failure 401 {object} ErrorResponse "刷新令牌无效"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/auth/refresh [post]
func (h *Handler) RefreshToken(c *gin.Context) {
	// 从 Cookie 获取刷新令牌
	refreshToken, err := c.Cookie("refresh_token")
	if err != nil {
		// 尝试从请求体获取
		var req struct {
			RefreshToken string `json:"refresh_token"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Code:      "MISSING_REFRESH_TOKEN",
				Message:   "缺少刷新令牌",
				Timestamp: time.Now().Format(time.RFC3339),
			})
			return
		}
		refreshToken = req.RefreshToken
	}
	
	tokenPair, err := h.service.RefreshToken(c.Request.Context(), refreshToken)
	if err != nil {
		h.logger.Error("刷新令牌失败", "error", err)
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:      "REFRESH_TOKEN_FAILED",
			Message:   "刷新令牌失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 更新刷新令牌 Cookie
	c.SetCookie(
		"refresh_token",
		tokenPair.RefreshToken,
		int(time.Hour*24*7/time.Second),
		"/",
		"",
		true,
		true,
	)
	
	h.logger.Info("令牌刷新成功")
	c.JSON(http.StatusOK, tokenPair)
}

// CheckPermission 检查权限
// @Summary 检查权限
// @Description 检查用户是否具有指定权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param request body PermissionCheckRequest true "权限检查请求"
// @Success 200 {object} PermissionCheckResponse "检查成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/permissions/check [post]
func (h *Handler) CheckPermission(c *gin.Context) {
	var req PermissionCheckRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定权限检查请求失败", "error", err)
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:      "INVALID_REQUEST",
			Message:   "请求参数格式错误",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	// 构建权限字符串
	permission := fmt.Sprintf("%s:%s", req.Resource, req.Action)
	if req.Scope != "" {
		permission += ":" + req.Scope
	}
	
	allowed, err := h.service.HasPermission(c.Request.Context(), req.UserID, permission)
	if err != nil {
		h.logger.Error("权限检查失败", "error", err, "user_id", req.UserID, "permission", permission)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:      "PERMISSION_CHECK_FAILED",
			Message:   "权限检查失败",
			Details:   err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
		})
		return
	}
	
	response := &PermissionCheckResponse{
		Allowed: allowed,
	}
	
	if !allowed {
		response.Reason = "权限不足"
	}
	
	c.JSON(http.StatusOK, response)
}
