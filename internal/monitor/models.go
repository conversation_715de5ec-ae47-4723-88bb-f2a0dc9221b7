package monitor

import (
	"time"
	"gorm.io/datatypes"
)

// MetricsRecord 指标记录
type MetricsRecord struct {
	ID            string    `json:"id" gorm:"primaryKey"`
	ContainerID   string    `json:"container_id" gorm:"not null;index"`
	Timestamp     time.Time `json:"timestamp" gorm:"not null;index"`
	CPUUsage      float64   `json:"cpu_usage"`      // CPU使用率 (%)
	MemoryUsage   int64     `json:"memory_usage"`   // 内存使用量 (字节)
	MemoryLimit   int64     `json:"memory_limit"`   // 内存限制 (字节)
	DiskUsage     int64     `json:"disk_usage"`     // 磁盘使用量 (字节)
	NetworkRx     int64     `json:"network_rx"`     // 网络接收字节
	NetworkTx     int64     `json:"network_tx"`     // 网络发送字节
	ProcessCount  int       `json:"process_count"`  // 进程数量
	FileDescCount int       `json:"file_desc_count"` // 文件描述符数量
	CreatedAt     time.Time `json:"created_at"`
}

// SystemMetricsRecord 系统指标记录
type SystemMetricsRecord struct {
	ID                string    `json:"id" gorm:"primaryKey"`
	Timestamp         time.Time `json:"timestamp" gorm:"not null;index"`
	TotalContainers   int       `json:"total_containers"`
	RunningContainers int       `json:"running_containers"`
	CPUUsage          float64   `json:"cpu_usage"`
	MemoryUsage       int64     `json:"memory_usage"`
	MemoryTotal       int64     `json:"memory_total"`
	DiskUsage         int64     `json:"disk_usage"`
	DiskTotal         int64     `json:"disk_total"`
	NetworkRx         int64     `json:"network_rx"`
	NetworkTx         int64     `json:"network_tx"`
	CreatedAt         time.Time `json:"created_at"`
}

// AlertRecord 告警记录
type AlertRecord struct {
	ID          string              `json:"id" gorm:"primaryKey"`
	AlertID     string              `json:"alert_id" gorm:"not null;index"`
	AlertName   string              `json:"alert_name" gorm:"not null"`
	Type        ResourceAlertType   `json:"type" gorm:"not null"`
	Severity    AlertSeverity       `json:"severity" gorm:"not null"`
	Status      AlertRecordStatus   `json:"status" gorm:"not null;index"`
	Message     string              `json:"message" gorm:"type:text"`
	Data        datatypes.JSON      `json:"data"`
	FiredAt     time.Time           `json:"fired_at" gorm:"not null;index"`
	ResolvedAt  *time.Time          `json:"resolved_at"`
	Duration    int64               `json:"duration"` // 持续时间(秒)
	TenantID    string              `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time           `json:"created_at"`
}

// AlertRecordStatus 告警记录状态
type AlertRecordStatus string

const (
	AlertRecordStatusFiring   AlertRecordStatus = "firing"
	AlertRecordStatusResolved AlertRecordStatus = "resolved"
	AlertRecordStatusSilenced AlertRecordStatus = "silenced"
)

// CleanupJob 清理任务
type CleanupJob struct {
	ID          string            `json:"id" gorm:"primaryKey"`
	PolicyID    string            `json:"policy_id" gorm:"not null;index"`
	PolicyName  string            `json:"policy_name" gorm:"not null"`
	Status      CleanupJobStatus  `json:"status" gorm:"not null;index"`
	StartTime   time.Time         `json:"start_time" gorm:"not null"`
	EndTime     *time.Time        `json:"end_time"`
	Duration    int64             `json:"duration"` // 执行时间(秒)
	ItemsTotal  int               `json:"items_total"`
	ItemsCleaned int              `json:"items_cleaned"`
	ItemsFailed int               `json:"items_failed"`
	Log         string            `json:"log" gorm:"type:text"`
	Error       string            `json:"error" gorm:"type:text"`
	TenantID    string            `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time         `json:"created_at"`
}

// CleanupJobStatus 清理任务状态
type CleanupJobStatus string

const (
	CleanupJobStatusRunning   CleanupJobStatus = "running"
	CleanupJobStatusCompleted CleanupJobStatus = "completed"
	CleanupJobStatusFailed    CleanupJobStatus = "failed"
	CleanupJobStatusCanceled  CleanupJobStatus = "canceled"
)

// ResourceQuota 资源配额
type ResourceQuota struct {
	ID          string               `json:"id" gorm:"primaryKey"`
	Name        string               `json:"name" gorm:"not null"`
	Description string               `json:"description" gorm:"type:text"`
	Type        ResourceQuotaType    `json:"type" gorm:"not null"`
	Limits      datatypes.JSON       `json:"limits" gorm:"not null"`
	Used        datatypes.JSON       `json:"used"`
	Status      ResourceQuotaStatus  `json:"status" gorm:"not null;index"`
	TenantID    string               `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string               `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
}

// ResourceQuotaType 资源配额类型
type ResourceQuotaType string

const (
	ResourceQuotaTypeTenant    ResourceQuotaType = "tenant"
	ResourceQuotaTypeUser      ResourceQuotaType = "user"
	ResourceQuotaTypeApp       ResourceQuotaType = "app"
	ResourceQuotaTypeNamespace ResourceQuotaType = "namespace"
)

// ResourceQuotaStatus 资源配额状态
type ResourceQuotaStatus string

const (
	ResourceQuotaStatusActive   ResourceQuotaStatus = "active"
	ResourceQuotaStatusInactive ResourceQuotaStatus = "inactive"
	ResourceQuotaStatusExceeded ResourceQuotaStatus = "exceeded"
)

// MonitoringRule 监控规则
type MonitoringRule struct {
	ID          string                `json:"id" gorm:"primaryKey"`
	Name        string                `json:"name" gorm:"not null"`
	Description string                `json:"description" gorm:"type:text"`
	Type        MonitoringRuleType    `json:"type" gorm:"not null"`
	Query       string                `json:"query" gorm:"not null"`
	Interval    int                   `json:"interval" gorm:"not null"` // 检查间隔(秒)
	Conditions  []MonitoringCondition `json:"conditions" gorm:"type:jsonb"`
	Actions     []MonitoringAction    `json:"actions" gorm:"type:jsonb"`
	Status      MonitoringRuleStatus  `json:"status" gorm:"not null;index"`
	LastRun     *time.Time            `json:"last_run"`
	NextRun     *time.Time            `json:"next_run"`
	TenantID    string                `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string                `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
}

// MonitoringRuleType 监控规则类型
type MonitoringRuleType string

const (
	MonitoringRuleTypeMetric MonitoringRuleType = "metric"
	MonitoringRuleTypeLog    MonitoringRuleType = "log"
	MonitoringRuleTypeEvent  MonitoringRuleType = "event"
)

// MonitoringRuleStatus 监控规则状态
type MonitoringRuleStatus string

const (
	MonitoringRuleStatusActive   MonitoringRuleStatus = "active"
	MonitoringRuleStatusInactive MonitoringRuleStatus = "inactive"
	MonitoringRuleStatusError    MonitoringRuleStatus = "error"
)

// MonitoringCondition 监控条件
type MonitoringCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Duration int         `json:"duration"` // 持续时间(秒)
}

// MonitoringAction 监控动作
type MonitoringAction struct {
	Type   string                 `json:"type"`
	Config map[string]interface{} `json:"config"`
}

// ResourceUsageHistory 资源使用历史
type ResourceUsageHistory struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	ResourceID  string    `json:"resource_id" gorm:"not null;index"`
	ResourceType string   `json:"resource_type" gorm:"not null"`
	Date        time.Time `json:"date" gorm:"not null;index"`
	CPUUsage    float64   `json:"cpu_usage"`
	MemoryUsage int64     `json:"memory_usage"`
	DiskUsage   int64     `json:"disk_usage"`
	NetworkRx   int64     `json:"network_rx"`
	NetworkTx   int64     `json:"network_tx"`
	Cost        float64   `json:"cost"`
	TenantID    string    `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time `json:"created_at"`
}

// PerformanceBaseline 性能基线
type PerformanceBaseline struct {
	ID            string                     `json:"id" gorm:"primaryKey"`
	Name          string                     `json:"name" gorm:"not null"`
	Description   string                     `json:"description" gorm:"type:text"`
	ResourceType  string                     `json:"resource_type" gorm:"not null"`
	Metrics       datatypes.JSON             `json:"metrics" gorm:"not null"`
	Thresholds    datatypes.JSON             `json:"thresholds"`
	Status        PerformanceBaselineStatus  `json:"status" gorm:"not null;index"`
	ValidFrom     time.Time                  `json:"valid_from" gorm:"not null"`
	ValidTo       *time.Time                 `json:"valid_to"`
	TenantID      string                     `json:"tenant_id" gorm:"not null;index"`
	CreatedBy     string                     `json:"created_by" gorm:"not null"`
	CreatedAt     time.Time                  `json:"created_at"`
	UpdatedAt     time.Time                  `json:"updated_at"`
}

// PerformanceBaselineStatus 性能基线状态
type PerformanceBaselineStatus string

const (
	PerformanceBaselineStatusActive   PerformanceBaselineStatus = "active"
	PerformanceBaselineStatusInactive PerformanceBaselineStatus = "inactive"
	PerformanceBaselineStatusExpired  PerformanceBaselineStatus = "expired"
)

// MonitoringDashboard 监控仪表板
type MonitoringDashboard struct {
	ID          string                     `json:"id" gorm:"primaryKey"`
	Name        string                     `json:"name" gorm:"not null"`
	Description string                     `json:"description" gorm:"type:text"`
	Layout      datatypes.JSON             `json:"layout" gorm:"not null"`
	Widgets     datatypes.JSON             `json:"widgets" gorm:"not null"`
	Filters     datatypes.JSON             `json:"filters"`
	Status      MonitoringDashboardStatus  `json:"status" gorm:"not null;index"`
	IsPublic    bool                       `json:"is_public" gorm:"default:false"`
	TenantID    string                     `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string                     `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

// MonitoringDashboardStatus 监控仪表板状态
type MonitoringDashboardStatus string

const (
	MonitoringDashboardStatusActive   MonitoringDashboardStatus = "active"
	MonitoringDashboardStatusInactive MonitoringDashboardStatus = "inactive"
	MonitoringDashboardStatusDraft    MonitoringDashboardStatus = "draft"
)

// AlertSilence 告警静默
type AlertSilence struct {
	ID          string             `json:"id" gorm:"primaryKey"`
	AlertID     string             `json:"alert_id" gorm:"not null;index"`
	Reason      string             `json:"reason" gorm:"not null"`
	Comment     string             `json:"comment" gorm:"type:text"`
	StartTime   time.Time          `json:"start_time" gorm:"not null"`
	EndTime     time.Time          `json:"end_time" gorm:"not null"`
	Status      AlertSilenceStatus `json:"status" gorm:"not null;index"`
	TenantID    string             `json:"tenant_id" gorm:"not null;index"`
	CreatedBy   string             `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
}

// AlertSilenceStatus 告警静默状态
type AlertSilenceStatus string

const (
	AlertSilenceStatusActive   AlertSilenceStatus = "active"
	AlertSilenceStatusExpired  AlertSilenceStatus = "expired"
	AlertSilenceStatusCanceled AlertSilenceStatus = "canceled"
)

// MonitoringNotification 监控通知
type MonitoringNotification struct {
	ID          string                        `json:"id" gorm:"primaryKey"`
	Type        MonitoringNotificationType    `json:"type" gorm:"not null"`
	Title       string                        `json:"title" gorm:"not null"`
	Message     string                        `json:"message" gorm:"type:text"`
	Data        datatypes.JSON                `json:"data"`
	Recipients  datatypes.JSON                `json:"recipients"`
	Status      MonitoringNotificationStatus  `json:"status" gorm:"not null;index"`
	SentAt      *time.Time                    `json:"sent_at"`
	Error       string                        `json:"error" gorm:"type:text"`
	TenantID    string                        `json:"tenant_id" gorm:"not null;index"`
	CreatedAt   time.Time                     `json:"created_at"`
}

// MonitoringNotificationType 监控通知类型
type MonitoringNotificationType string

const (
	MonitoringNotificationTypeAlert   MonitoringNotificationType = "alert"
	MonitoringNotificationTypeReport  MonitoringNotificationType = "report"
	MonitoringNotificationTypeWarning MonitoringNotificationType = "warning"
)

// MonitoringNotificationStatus 监控通知状态
type MonitoringNotificationStatus string

const (
	MonitoringNotificationStatusPending MonitoringNotificationStatus = "pending"
	MonitoringNotificationStatusSent    MonitoringNotificationStatus = "sent"
	MonitoringNotificationStatusFailed  MonitoringNotificationStatus = "failed"
)

// TableName 指定表名
func (MetricsRecord) TableName() string {
	return "metrics_records"
}

func (SystemMetricsRecord) TableName() string {
	return "system_metrics_records"
}

func (AlertRecord) TableName() string {
	return "alert_records"
}

func (CleanupJob) TableName() string {
	return "cleanup_jobs"
}

func (ResourceQuota) TableName() string {
	return "resource_quotas"
}

func (MonitoringRule) TableName() string {
	return "monitoring_rules"
}

func (ResourceUsageHistory) TableName() string {
	return "resource_usage_history"
}

func (PerformanceBaseline) TableName() string {
	return "performance_baselines"
}

func (MonitoringDashboard) TableName() string {
	return "monitoring_dashboards"
}

func (AlertSilence) TableName() string {
	return "alert_silences"
}

func (MonitoringNotification) TableName() string {
	return "monitoring_notifications"
}
