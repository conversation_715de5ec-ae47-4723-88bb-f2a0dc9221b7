<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h2 class="welcome-title">
          欢迎回来，{{ userStore.user?.displayName }}！
        </h2>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，祝您工作愉快
        </p>
      </div>
      <div class="welcome-actions">
        <el-button type="primary" @click="$router.push('/apps/create')">
          <el-icon><Plus /></el-icon>
          创建应用
        </el-button>
        <el-button @click="$router.push('/cicd/pipelines')">
          <el-icon><Connection /></el-icon>
          管理流水线
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in statsData" :key="stat.key">
        <div class="stat-icon" :class="`stat-icon-${stat.type}`">
          <el-icon><component :is="stat.icon" /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-change" :class="stat.changeType">
            <el-icon>
              <ArrowUp v-if="stat.changeType === 'increase'" />
              <ArrowDown v-else-if="stat.changeType === 'decrease'" />
              <Minus v-else />
            </el-icon>
            {{ stat.change }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表和列表区域 -->
    <div class="dashboard-content">
      <div class="content-left">
        <!-- 应用状态图表 -->
        <div class="chart-card">
          <div class="card-header">
            <h3>应用状态分布</h3>
            <el-button type="text" @click="refreshCharts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="appStatusChartOption" 
              autoresize
            />
          </div>
        </div>
        
        <!-- 资源使用情况 -->
        <div class="chart-card">
          <div class="card-header">
            <h3>资源使用趋势</h3>
            <el-select v-model="resourceTimeRange" size="small">
              <el-option label="最近1小时" value="1h" />
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
            </el-select>
          </div>
          <div class="chart-container">
            <v-chart 
              class="chart" 
              :option="resourceChartOption" 
              autoresize
            />
          </div>
        </div>
      </div>
      
      <div class="content-right">
        <!-- 最近活动 -->
        <div class="activity-card">
          <div class="card-header">
            <h3>最近活动</h3>
            <el-button type="text" @click="$router.push('/monitor/logs')">
              查看全部
            </el-button>
          </div>
          <div class="activity-list">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="`activity-${activity.type}`">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-status" :class="activity.status">
                {{ activity.statusText }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions-card">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          <div class="quick-actions">
            <div 
              v-for="action in quickActions" 
              :key="action.key"
              class="quick-action-item"
              @click="handleQuickAction(action.key)"
            >
              <el-icon><component :is="action.icon" /></el-icon>
              <span>{{ action.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const userStore = useUserStore()
const resourceTimeRange = ref('24h')

// 当前日期
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

// 统计数据
const statsData = ref([
  {
    key: 'apps',
    label: '运行中应用',
    value: '24',
    change: '+12%',
    changeType: 'increase',
    icon: 'Box',
    type: 'primary'
  },
  {
    key: 'builds',
    label: '今日构建',
    value: '156',
    change: '+8%',
    changeType: 'increase',
    icon: 'Tools',
    type: 'success'
  },
  {
    key: 'deployments',
    label: '成功部署',
    value: '98.5%',
    change: '+2.1%',
    changeType: 'increase',
    icon: 'Upload',
    type: 'warning'
  },
  {
    key: 'users',
    label: '活跃用户',
    value: '1,234',
    change: '+5%',
    changeType: 'increase',
    icon: 'User',
    type: 'info'
  }
])

// 最近活动数据
const recentActivities = ref([
  {
    id: '1',
    type: 'deploy',
    icon: 'Upload',
    title: '应用 "user-service" 部署成功',
    time: '2分钟前',
    status: 'success',
    statusText: '成功'
  },
  {
    id: '2',
    type: 'build',
    icon: 'Tools',
    title: '构建 #156 开始执行',
    time: '5分钟前',
    status: 'running',
    statusText: '进行中'
  },
  {
    id: '3',
    type: 'user',
    icon: 'User',
    title: '新用户 "张三" 注册',
    time: '10分钟前',
    status: 'info',
    statusText: '完成'
  },
  {
    id: '4',
    type: 'config',
    icon: 'Setting',
    title: '配置 "database.url" 已更新',
    time: '15分钟前',
    status: 'warning',
    statusText: '已更新'
  }
])

// 快速操作
const quickActions = ref([
  { key: 'create-app', label: '创建应用', icon: 'Plus' },
  { key: 'view-logs', label: '查看日志', icon: 'Document' },
  { key: 'manage-users', label: '用户管理', icon: 'User' },
  { key: 'system-config', label: '系统配置', icon: 'Setting' }
])

// 应用状态图表配置
const appStatusChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '应用状态',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 18, name: '运行中' },
        { value: 4, name: '停止' },
        { value: 2, name: '构建中' },
        { value: 1, name: '失败' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 资源使用图表配置
const resourceChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['CPU使用率', '内存使用率', '磁盘使用率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
  },
  yAxis: {
    type: 'value',
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: 'CPU使用率',
      type: 'line',
      data: [20, 25, 30, 45, 60, 55, 40],
      smooth: true
    },
    {
      name: '内存使用率',
      type: 'line',
      data: [30, 35, 40, 50, 65, 70, 60],
      smooth: true
    },
    {
      name: '磁盘使用率',
      type: 'line',
      data: [15, 18, 20, 25, 30, 32, 28],
      smooth: true
    }
  ]
}))

// 方法定义
const refreshCharts = () => {
  ElMessage.success('图表数据已刷新')
  // TODO: 实际的数据刷新逻辑
}

const handleQuickAction = (actionKey: string) => {
  switch (actionKey) {
    case 'create-app':
      router.push('/apps/create')
      break
    case 'view-logs':
      router.push('/monitor/logs')
      break
    case 'manage-users':
      router.push('/users')
      break
    case 'system-config':
      router.push('/config')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  // TODO: 加载实际的统计数据
  console.log('加载仪表板数据')
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  .welcome-section {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
    color: white;
    
    .welcome-content {
      .welcome-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }
      
      .welcome-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin: 0;
      }
    }
    
    .welcome-actions {
      display: flex;
      gap: 12px;
      
      .el-button {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      @include card-style;
      display: flex;
      align-items: center;
      padding: 24px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          font-size: 24px;
          color: white;
        }
        
        &.stat-icon-primary {
          background: var(--el-color-primary);
        }
        
        &.stat-icon-success {
          background: var(--el-color-success);
        }
        
        &.stat-icon-warning {
          background: var(--el-color-warning);
        }
        
        &.stat-icon-info {
          background: var(--el-color-info);
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          margin-bottom: 8px;
        }
        
        .stat-change {
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          
          &.increase {
            color: var(--el-color-success);
          }
          
          &.decrease {
            color: var(--el-color-danger);
          }
          
          &.stable {
            color: var(--el-color-info);
          }
        }
      }
    }
  }
  
  .dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    
    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
    
    .content-left {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .content-right {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
    
    .chart-card,
    .activity-card,
    .quick-actions-card {
      @include card-style;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin: 0;
        }
      }
      
      .chart-container {
        height: 300px;
        
        .chart {
          height: 100%;
          width: 100%;
        }
      }
    }
    
    .activity-list {
      .activity-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-extra-light);
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          
          .el-icon {
            font-size: 16px;
            color: white;
          }
          
          &.activity-deploy {
            background: var(--el-color-primary);
          }
          
          &.activity-build {
            background: var(--el-color-warning);
          }
          
          &.activity-user {
            background: var(--el-color-success);
          }
          
          &.activity-config {
            background: var(--el-color-info);
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-size: 14px;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
        
        .activity-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 12px;
          
          &.success {
            background: var(--el-color-success-light-8);
            color: var(--el-color-success);
          }
          
          &.running {
            background: var(--el-color-warning-light-8);
            color: var(--el-color-warning);
          }
          
          &.info {
            background: var(--el-color-info-light-8);
            color: var(--el-color-info);
          }
          
          &.warning {
            background: var(--el-color-warning-light-8);
            color: var(--el-color-warning);
          }
        }
      }
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      
      .quick-action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
          transform: translateY(-2px);
        }
        
        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
          margin-bottom: 8px;
        }
        
        span {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
}
</style>
