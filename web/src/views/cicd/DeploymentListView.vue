<template>
  <div class="deployment-list-container">
    <!-- 页面头部 -->
    <PageHeader
      title="部署管理"
      description="管理应用的部署历史和环境配置"
      icon="Upload"
    >
      <template #actions>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建部署
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select
          v-model="filterEnvironment"
          placeholder="选择环境"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部环境" value="" />
          <el-option label="开发环境" value="dev" />
          <el-option label="测试环境" value="test" />
          <el-option label="预发布环境" value="staging" />
          <el-option label="生产环境" value="prod" />
        </el-select>
        
        <el-select
          v-model="filterStatus"
          placeholder="部署状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="部署中" value="deploying" />
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
          <el-option label="回滚中" value="rollback" />
        </el-select>
        
        <el-input
          v-model="searchKeyword"
          placeholder="搜索应用名称..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 部署列表 -->
    <LoadingCard :loading="loading">
      <el-table :data="deploymentList" class="deployment-table">
        <el-table-column label="部署ID" width="100">
          <template #default="{ row }">
            <el-button 
              type="text" 
              @click="viewDeployment(row.id)"
              class="deployment-id"
            >
              #{{ row.id.substring(0, 8) }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column label="应用信息" min-width="200">
          <template #default="{ row }">
            <div class="app-info">
              <div class="app-name">{{ row.applicationName }}</div>
              <div class="app-version">版本: {{ row.version || 'latest' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="环境" width="100">
          <template #default="{ row }">
            <el-tag :type="getEnvironmentTagType(row.environment)">
              {{ getEnvironmentLabel(row.environment) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <StatusTag :status="row.status" type="deployment" />
          </template>
        </el-table-column>
        
        <el-table-column label="部署策略" width="120">
          <template #default="{ row }">
            <span class="strategy">{{ getStrategyLabel(row.strategy) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="实例数" width="80">
          <template #default="{ row }">
            <span class="instance-count">
              {{ row.currentInstances }}/{{ row.targetInstances }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="部署时间" width="150">
          <template #default="{ row }">
            <span v-if="row.deployedAt">{{ formatTime(row.deployedAt) }}</span>
            <span v-else class="text-placeholder">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="持续时间" width="100">
          <template #default="{ row }">
            <span v-if="row.duration" class="duration">
              {{ formatDuration(row.duration) }}
            </span>
            <span v-else-if="row.status === 'deploying'" class="duration running">
              {{ getRunningDuration(row.startedAt) }}
            </span>
            <span v-else class="text-placeholder">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                @click="viewDeploymentLogs(row.id)"
              >
                日志
              </el-button>
              
              <el-dropdown @command="(cmd) => handleDeploymentAction(cmd, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="rollback"
                      :disabled="row.status !== 'success'"
                    >
                      <el-icon><RefreshLeft /></el-icon>回滚
                    </el-dropdown-item>
                    <el-dropdown-item command="redeploy">
                      <el-icon><Refresh /></el-icon>重新部署
                    </el-dropdown-item>
                    <el-dropdown-item command="scale">
                      <el-icon><Rank /></el-icon>扩缩容
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </LoadingCard>
    
    <!-- 创建部署对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建部署"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="应用" prop="applicationId">
          <el-select
            v-model="createForm.applicationId"
            placeholder="选择应用"
            class="w-full"
          >
            <el-option
              v-for="app in applicationOptions"
              :key="app.id"
              :label="app.name"
              :value="app.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="环境" prop="environment">
          <el-select
            v-model="createForm.environment"
            placeholder="选择环境"
            class="w-full"
          >
            <el-option label="开发环境" value="dev" />
            <el-option label="测试环境" value="test" />
            <el-option label="预发布环境" value="staging" />
            <el-option label="生产环境" value="prod" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本" prop="version">
          <el-input
            v-model="createForm.version"
            placeholder="latest"
          />
        </el-form-item>
        
        <el-form-item label="部署策略" prop="strategy">
          <el-radio-group v-model="createForm.strategy">
            <el-radio label="rolling">滚动更新</el-radio>
            <el-radio label="blue-green">蓝绿部署</el-radio>
            <el-radio label="canary">金丝雀部署</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="实例数量" prop="instances">
          <el-input-number
            v-model="createForm.instances"
            :min="1"
            :max="100"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="部署描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="creating"
          @click="handleCreate"
        >
          开始部署
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 部署日志对话框 -->
    <el-dialog
      v-model="logsDialogVisible"
      :title="`部署 #${currentDeployment?.id?.substring(0, 8)} 日志`"
      width="80%"
      top="5vh"
    >
      <div class="logs-container">
        <div class="logs-header">
          <el-button size="small" @click="refreshDeploymentLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" @click="downloadDeploymentLogs">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
        
        <div class="logs-content">
          <pre class="logs-text">{{ deploymentLogs || '暂无日志数据' }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { cicdApi } from '@/api/cicd'
import { appsApi } from '@/api/apps'
import type { FormInstance, FormRules } from 'element-plus'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingCard from '@/components/common/LoadingCard.vue'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const loading = ref(false)
const creating = ref(false)
const createDialogVisible = ref(false)
const logsDialogVisible = ref(false)
const filterEnvironment = ref('')
const filterStatus = ref('')
const searchKeyword = ref('')
const deploymentList = ref<any[]>([])
const applicationOptions = ref<any[]>([])
const currentDeployment = ref<any>(null)
const deploymentLogs = ref('')
const createFormRef = ref<FormInstance>()

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 创建表单
const createForm = reactive({
  applicationId: '',
  environment: '',
  version: 'latest',
  strategy: 'rolling',
  instances: 1,
  description: ''
})

// 表单验证规则
const createRules: FormRules = {
  applicationId: [
    { required: true, message: '请选择应用', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ],
  instances: [
    { required: true, message: '请输入实例数量', trigger: 'blur' }
  ]
}

// 计算属性
const queryParams = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  environment: filterEnvironment.value,
  status: filterStatus.value,
  search: searchKeyword.value
}))

// 方法定义
const loadDeploymentList = async () => {
  try {
    loading.value = true
    const response = await cicdApi.getDeployments(queryParams.value)
    deploymentList.value = response.data.items
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载部署列表失败:', error)
    ElMessage.error('加载部署列表失败')
  } finally {
    loading.value = false
  }
}

const loadApplicationOptions = async () => {
  try {
    const response = await appsApi.getApps({ pageSize: 1000 })
    applicationOptions.value = response.data.items
  } catch (error) {
    console.error('加载应用选项失败:', error)
  }
}

const handleSearch = debounce(() => {
  pagination.page = 1
  loadDeploymentList()
}, 300)

const handleFilter = () => {
  pagination.page = 1
  loadDeploymentList()
}

const refreshList = () => {
  loadDeploymentList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadDeploymentList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadDeploymentList()
}

const viewDeployment = (id: string) => {
  router.push(`/cicd/deployments/${id}`)
}

const showCreateDialog = () => {
  createDialogVisible.value = true
}

const handleCreate = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    await cicdApi.createDeployment(createForm)
    ElMessage.success('部署已开始')
    createDialogVisible.value = false
    await loadDeploymentList()
    
    // 重置表单
    Object.assign(createForm, {
      applicationId: '',
      environment: '',
      version: 'latest',
      strategy: 'rolling',
      instances: 1,
      description: ''
    })
  } catch (error) {
    console.error('创建部署失败:', error)
  } finally {
    creating.value = false
  }
}

const viewDeploymentLogs = async (deploymentId: string) => {
  try {
    const deployment = deploymentList.value.find(d => d.id === deploymentId)
    if (!deployment) return
    
    currentDeployment.value = deployment
    logsDialogVisible.value = true
    
    // TODO: 实现获取部署日志的API
    deploymentLogs.value = '部署日志功能开发中...'
  } catch (error) {
    console.error('加载部署日志失败:', error)
    ElMessage.error('加载部署日志失败')
  }
}

const refreshDeploymentLogs = () => {
  if (currentDeployment.value) {
    viewDeploymentLogs(currentDeployment.value.id)
  }
}

const downloadDeploymentLogs = () => {
  if (!deploymentLogs.value || !currentDeployment.value) return
  
  const blob = new Blob([deploymentLogs.value], { type: 'text/plain' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `deployment-${currentDeployment.value.id.substring(0, 8)}-logs.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

const handleDeploymentAction = async (command: string, deployment: any) => {
  switch (command) {
    case 'rollback':
      await rollbackDeployment(deployment)
      break
    case 'redeploy':
      // TODO: 实现重新部署
      ElMessage.info('重新部署功能开发中...')
      break
    case 'scale':
      // TODO: 实现扩缩容
      ElMessage.info('扩缩容功能开发中...')
      break
  }
}

const rollbackDeployment = async (deployment: any) => {
  try {
    await ElMessageBox.confirm('确定要回滚这个部署吗？', '确认回滚', {
      type: 'warning'
    })
    
    await cicdApi.rollbackDeployment(deployment.id)
    ElMessage.success('回滚已开始')
    await loadDeploymentList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('回滚部署失败:', error)
    }
  }
}

// 工具函数
const getEnvironmentTagType = (env: string) => {
  const typeMap: Record<string, string> = {
    dev: 'info',
    test: 'warning',
    staging: 'primary',
    prod: 'danger'
  }
  return typeMap[env] || ''
}

const getEnvironmentLabel = (env: string) => {
  const labelMap: Record<string, string> = {
    dev: '开发',
    test: '测试',
    staging: '预发布',
    prod: '生产'
  }
  return labelMap[env] || env
}

const getStrategyLabel = (strategy: string) => {
  const labelMap: Record<string, string> = {
    rolling: '滚动更新',
    'blue-green': '蓝绿部署',
    canary: '金丝雀部署'
  }
  return labelMap[strategy] || strategy
}

const formatTime = (time?: string) => {
  return time ? dayjs(time).format('MM-DD HH:mm') : '-'
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const getRunningDuration = (startTime?: string) => {
  if (!startTime) return '-'
  const start = dayjs(startTime)
  const now = dayjs()
  const duration = now.diff(start, 'second')
  return formatDuration(duration)
}

function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadApplicationOptions()
  loadDeploymentList()
})
</script>

<style lang="scss" scoped>
.deployment-list-container {
  .filter-section {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      
      .filter-select {
        width: 120px;
      }
      
      .search-input {
        width: 250px;
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        width: 100%;
        flex-wrap: wrap;
        
        .filter-select,
        .search-input {
          width: 100%;
        }
      }
    }
  }
  
  .deployment-table {
    .deployment-id {
      font-weight: 600;
      color: var(--el-color-primary);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
    
    .app-info {
      .app-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .app-version {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .strategy {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
    
    .instance-count {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 500;
      color: var(--el-color-primary);
    }
    
    .duration {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      
      &.running {
        color: var(--el-color-warning);
        font-weight: 500;
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .logs-container {
    .logs-header {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .logs-content {
      background: #1e1e1e;
      border-radius: 4px;
      padding: 16px;
      max-height: 60vh;
      overflow-y: auto;
      
      .logs-text {
        color: #d4d4d4;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>
