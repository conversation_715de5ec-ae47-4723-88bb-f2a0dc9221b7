<template>
  <div class="pipeline-list-container">
    <!-- 页面头部 -->
    <PageHeader
      title="流水线管理"
      description="管理您的CI/CD流水线，自动化构建和部署流程"
      icon="Connection"
    >
      <template #actions>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建流水线
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索流水线名称..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterStatus"
          placeholder="状态筛选"
          clearable
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <!-- 流水线列表 -->
    <LoadingCard :loading="loading">
      <div class="pipeline-grid">
        <div 
          v-for="pipeline in pipelineList" 
          :key="pipeline.id"
          class="pipeline-card"
          @click="viewPipeline(pipeline.id)"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="pipeline-info">
              <h3 class="pipeline-name">{{ pipeline.name }}</h3>
              <p class="pipeline-description">{{ pipeline.description || '暂无描述' }}</p>
            </div>
            <div class="pipeline-status">
              <StatusTag :status="pipeline.status" type="general" />
            </div>
          </div>
          
          <!-- 仓库信息 -->
          <div class="repository-info">
            <div class="repo-item">
              <el-icon><Link /></el-icon>
              <span class="repo-url">{{ pipeline.repository.url }}</span>
            </div>
            <div class="repo-item">
              <el-icon><Branch /></el-icon>
              <span class="repo-branch">{{ pipeline.repository.branch }}</span>
            </div>
          </div>
          
          <!-- 构建统计 -->
          <div class="build-stats">
            <div class="stat-item">
              <span class="stat-label">总构建:</span>
              <span class="stat-value">{{ pipeline.totalBuilds || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率:</span>
              <span class="stat-value success">{{ pipeline.successRate || '0%' }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最后构建:</span>
              <span class="stat-value">{{ formatTime(pipeline.lastBuildAt) }}</span>
            </div>
          </div>
          
          <!-- 卡片操作 -->
          <div class="card-actions" @click.stop>
            <el-button size="small" type="primary" @click="triggerBuild(pipeline)">
              <el-icon><VideoPlay /></el-icon>
              触发构建
            </el-button>
            <el-dropdown @command="(cmd) => handlePipelineAction(cmd, pipeline)">
              <el-button size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    <el-icon><Edit /></el-icon>编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="clone">
                    <el-icon><CopyDocument /></el-icon>克隆
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="pipeline.status === 'active' ? 'disable' : 'enable'"
                  >
                    <el-icon><Switch /></el-icon>
                    {{ pipeline.status === 'active' ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item divided command="delete" class="danger-item">
                    <el-icon><Delete /></el-icon>删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <el-empty 
        v-if="!loading && pipelineList.length === 0"
        description="暂无流水线数据"
        class="empty-state"
      >
        <el-button type="primary" @click="showCreateDialog">
          创建第一个流水线
        </el-button>
      </el-empty>
      
      <!-- 分页 -->
      <div v-if="pipelineList.length > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[12, 24, 48]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </LoadingCard>
    
    <!-- 创建流水线对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建流水线"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="流水线名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入流水线名称"
            maxlength="50"
          />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入流水线描述"
            maxlength="200"
          />
        </el-form-item>
        
        <el-form-item label="仓库地址" prop="repository.url">
          <el-input
            v-model="createForm.repository.url"
            placeholder="https://github.com/user/repo.git"
          />
        </el-form-item>
        
        <el-form-item label="分支" prop="repository.branch">
          <el-input
            v-model="createForm.repository.branch"
            placeholder="main"
          />
        </el-form-item>
        
        <el-form-item label="仓库类型" prop="repository.type">
          <el-radio-group v-model="createForm.repository.type">
            <el-radio label="gitea">Gitea</el-radio>
            <el-radio label="github">GitHub</el-radio>
            <el-radio label="gitlab">GitLab</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="creating"
          @click="handleCreate"
        >
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { cicdApi } from '@/api/cicd'
import type { Pipeline } from '@/types'
import type { FormInstance, FormRules } from 'element-plus'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingCard from '@/components/common/LoadingCard.vue'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const loading = ref(false)
const creating = ref(false)
const createDialogVisible = ref(false)
const searchKeyword = ref('')
const filterStatus = ref('')
const pipelineList = ref<Pipeline[]>([])
const createFormRef = ref<FormInstance>()

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 12,
  total: 0
})

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  repository: {
    url: '',
    branch: 'main',
    type: 'gitea' as const
  },
  stages: [],
  triggers: []
})

// 表单验证规则
const createRules: FormRules = {
  name: [
    { required: true, message: '请输入流水线名称', trigger: 'blur' },
    { min: 3, max: 50, message: '名称长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  'repository.url': [
    { required: true, message: '请输入仓库地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  'repository.branch': [
    { required: true, message: '请输入分支名称', trigger: 'blur' }
  ]
}

// 计算属性
const queryParams = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  search: searchKeyword.value,
  status: filterStatus.value
}))

// 方法定义
const loadPipelineList = async () => {
  try {
    loading.value = true
    const response = await cicdApi.getPipelines(queryParams.value)
    pipelineList.value = response.data.items
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载流水线列表失败:', error)
    ElMessage.error('加载流水线列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = debounce(() => {
  pagination.page = 1
  loadPipelineList()
}, 300)

const handleFilter = () => {
  pagination.page = 1
  loadPipelineList()
}

const refreshList = () => {
  loadPipelineList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadPipelineList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadPipelineList()
}

const viewPipeline = (id: string) => {
  router.push(`/cicd/pipelines/${id}`)
}

const showCreateDialog = () => {
  createDialogVisible.value = true
}

const handleCreate = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    creating.value = true
    
    await cicdApi.createPipeline(createForm)
    ElMessage.success('流水线创建成功')
    createDialogVisible.value = false
    await loadPipelineList()
    
    // 重置表单
    Object.assign(createForm, {
      name: '',
      description: '',
      repository: {
        url: '',
        branch: 'main',
        type: 'gitea'
      },
      stages: [],
      triggers: []
    })
  } catch (error) {
    console.error('创建流水线失败:', error)
  } finally {
    creating.value = false
  }
}

const triggerBuild = async (pipeline: Pipeline) => {
  try {
    await cicdApi.createBuild({
      pipelineId: pipeline.id,
      branch: pipeline.repository.branch
    })
    ElMessage.success('构建已触发')
  } catch (error) {
    console.error('触发构建失败:', error)
  }
}

const handlePipelineAction = async (command: string, pipeline: Pipeline) => {
  switch (command) {
    case 'edit':
      // TODO: 实现编辑功能
      ElMessage.info('编辑功能开发中...')
      break
    case 'clone':
      // TODO: 实现克隆功能
      ElMessage.info('克隆功能开发中...')
      break
    case 'enable':
    case 'disable':
      // TODO: 实现启用/禁用功能
      ElMessage.info('状态切换功能开发中...')
      break
    case 'delete':
      await deletePipeline(pipeline)
      break
  }
}

const deletePipeline = async (pipeline: Pipeline) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除流水线 "${pipeline.name}" 吗？此操作不可恢复！`,
      '确认删除',
      { type: 'error' }
    )
    
    await cicdApi.deletePipeline(pipeline.id)
    ElMessage.success('流水线已删除')
    await loadPipelineList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除流水线失败:', error)
    }
  }
}

// 工具函数
const formatTime = (time?: string) => {
  return time ? dayjs(time).fromNow() : '从未'
}

function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPipelineList()
})
</script>

<style lang="scss" scoped>
.pipeline-list-container {
  .filter-section {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      
      .search-input {
        width: 300px;
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      
      .filter-left {
        width: 100%;
        
        .search-input {
          width: 100%;
        }
      }
    }
  }
  
  .pipeline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
    
    .pipeline-card {
      @include card-style;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .pipeline-info {
          flex: 1;
          
          .pipeline-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin: 0 0 8px 0;
          }
          
          .pipeline-description {
            font-size: 14px;
            color: var(--el-text-color-secondary);
            margin: 0;
            @include text-ellipsis(2);
          }
        }
      }
      
      .repository-info {
        margin-bottom: 16px;
        
        .repo-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .el-icon {
            font-size: 14px;
          }
          
          .repo-url {
            @include text-ellipsis;
            max-width: 250px;
          }
        }
      }
      
      .build-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding: 12px;
        background: var(--el-fill-color-extra-light);
        border-radius: 6px;
        
        .stat-item {
          text-align: center;
          
          .stat-label {
            display: block;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            margin-bottom: 4px;
          }
          
          .stat-value {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            
            &.success {
              color: var(--el-color-success);
            }
          }
        }
      }
      
      .card-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  .empty-state {
    margin: 40px 0;
  }
  
  .pagination-container {
    margin-top: 24px;
    display: flex;
    justify-content: center;
  }
}

:deep(.danger-item) {
  color: var(--el-color-danger);
}
</style>
