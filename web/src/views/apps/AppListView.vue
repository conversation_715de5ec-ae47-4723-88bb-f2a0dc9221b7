<template>
  <div class="app-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">应用管理</h2>
        <p class="page-description">管理您的应用生命周期，包括部署、扩缩容、监控等</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/apps/create')">
          <el-icon><Plus /></el-icon>
          创建应用
        </el-button>
      </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索应用名称或描述..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="filterStatus"
          placeholder="应用状态"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部状态" value="" />
          <el-option label="运行中" value="running" />
          <el-option label="已停止" value="stopped" />
          <el-option label="构建中" value="building" />
          <el-option label="部署中" value="deploying" />
          <el-option label="失败" value="failed" />
        </el-select>
        
        <el-select
          v-model="filterLanguage"
          placeholder="运行时"
          clearable
          class="filter-select"
          @change="handleFilter"
        >
          <el-option label="全部语言" value="" />
          <el-option label="Node.js" value="nodejs" />
          <el-option label="Python" value="python" />
          <el-option label="Go" value="go" />
          <el-option label="Java" value="java" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="showBatchActions = !showBatchActions">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
      </div>
    </div>
    
    <!-- 批量操作栏 -->
    <div v-show="showBatchActions" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedApps.length }} 个应用
      </div>
      <div class="batch-buttons">
        <el-button size="small" @click="batchStart">批量启动</el-button>
        <el-button size="small" @click="batchStop">批量停止</el-button>
        <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>
    
    <!-- 应用列表 -->
    <div class="app-list">
      <el-table
        v-loading="loading"
        :data="appList"
        @selection-change="handleSelectionChange"
        class="app-table"
        empty-text="暂无应用数据"
      >
        <el-table-column 
          v-if="showBatchActions"
          type="selection" 
          width="55" 
        />
        
        <el-table-column label="应用信息" min-width="200">
          <template #default="{ row }">
            <div class="app-info">
              <div class="app-avatar">
                <el-icon><Box /></el-icon>
              </div>
              <div class="app-details">
                <div class="app-name">{{ row.displayName || row.name }}</div>
                <div class="app-description">{{ row.description || '暂无描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="运行时" width="120">
          <template #default="{ row }">
            <el-tag :type="getRuntimeTagType(row.runtime.language)">
              {{ getRuntimeLabel(row.runtime) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="实例数" width="80">
          <template #default="{ row }">
            <span class="instance-count">
              {{ row.instances?.length || 0 }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="资源配置" width="150">
          <template #default="{ row }">
            <div class="resource-info">
              <div>CPU: {{ row.resources?.cpu || 'N/A' }}</div>
              <div>内存: {{ row.resources?.memory || 'N/A' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="最后部署" width="150">
          <template #default="{ row }">
            <span v-if="row.lastDeployedAt" class="deploy-time">
              {{ formatTime(row.lastDeployedAt) }}
            </span>
            <span v-else class="text-placeholder">未部署</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                type="primary" 
                @click="viewApp(row.id)"
              >
                查看
              </el-button>
              
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="start" 
                      :disabled="row.status === 'running'"
                    >
                      <el-icon><VideoPlay /></el-icon>启动
                    </el-dropdown-item>
                    <el-dropdown-item 
                      command="stop" 
                      :disabled="row.status === 'stopped'"
                    >
                      <el-icon><VideoPause /></el-icon>停止
                    </el-dropdown-item>
                    <el-dropdown-item command="restart">
                      <el-icon><Refresh /></el-icon>重启
                    </el-dropdown-item>
                    <el-dropdown-item command="deploy">
                      <el-icon><Upload /></el-icon>部署
                    </el-dropdown-item>
                    <el-dropdown-item divided command="edit">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { appsApi } from '@/api/apps'
import type { Application } from '@/types'
import dayjs from 'dayjs'

// 响应式数据
const router = useRouter()
const loading = ref(false)
const showBatchActions = ref(false)
const searchKeyword = ref('')
const filterStatus = ref('')
const filterLanguage = ref('')
const selectedApps = ref<Application[]>([])
const appList = ref<Application[]>([])

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const queryParams = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  search: searchKeyword.value,
  status: filterStatus.value,
  language: filterLanguage.value
}))

// 方法定义
const loadAppList = async () => {
  try {
    loading.value = true
    const response = await appsApi.getApps(queryParams.value)
    appList.value = response.data.items
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载应用列表失败:', error)
    ElMessage.error('加载应用列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = debounce(() => {
  pagination.page = 1
  loadAppList()
}, 300)

const handleFilter = () => {
  pagination.page = 1
  loadAppList()
}

const refreshList = () => {
  loadAppList()
}

const handleSelectionChange = (selection: Application[]) => {
  selectedApps.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadAppList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadAppList()
}

const viewApp = (id: string) => {
  router.push(`/apps/${id}`)
}

const handleAction = async (command: string, app: Application) => {
  switch (command) {
    case 'start':
      await startApp(app)
      break
    case 'stop':
      await stopApp(app)
      break
    case 'restart':
      await restartApp(app)
      break
    case 'deploy':
      await deployApp(app)
      break
    case 'edit':
      // TODO: 实现编辑功能
      ElMessage.info('编辑功能开发中...')
      break
    case 'delete':
      await deleteApp(app)
      break
  }
}

const startApp = async (app: Application) => {
  try {
    await appsApi.startApp(app.id)
    ElMessage.success(`应用 "${app.displayName}" 启动成功`)
    await loadAppList()
  } catch (error) {
    console.error('启动应用失败:', error)
  }
}

const stopApp = async (app: Application) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止应用 "${app.displayName}" 吗？`,
      '确认停止',
      { type: 'warning' }
    )
    
    await appsApi.stopApp(app.id)
    ElMessage.success(`应用 "${app.displayName}" 已停止`)
    await loadAppList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止应用失败:', error)
    }
  }
}

const restartApp = async (app: Application) => {
  try {
    await appsApi.restartApp(app.id)
    ElMessage.success(`应用 "${app.displayName}" 重启成功`)
    await loadAppList()
  } catch (error) {
    console.error('重启应用失败:', error)
  }
}

const deployApp = async (app: Application) => {
  try {
    await appsApi.deployApp(app.id)
    ElMessage.success(`应用 "${app.displayName}" 部署已开始`)
    await loadAppList()
  } catch (error) {
    console.error('部署应用失败:', error)
  }
}

const deleteApp = async (app: Application) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${app.displayName}" 吗？此操作不可恢复！`,
      '确认删除',
      { 
        type: 'error',
        confirmButtonText: '确定删除',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    await appsApi.deleteApp(app.id)
    ElMessage.success(`应用 "${app.displayName}" 已删除`)
    await loadAppList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除应用失败:', error)
    }
  }
}

// 批量操作
const batchStart = async () => {
  // TODO: 实现批量启动
  ElMessage.info('批量启动功能开发中...')
}

const batchStop = async () => {
  // TODO: 实现批量停止
  ElMessage.info('批量停止功能开发中...')
}

const batchDelete = async () => {
  // TODO: 实现批量删除
  ElMessage.info('批量删除功能开发中...')
}

// 工具函数
const getRuntimeTagType = (language: string) => {
  const typeMap: Record<string, string> = {
    nodejs: 'success',
    python: 'warning',
    go: 'info',
    java: 'danger'
  }
  return typeMap[language] || ''
}

const getRuntimeLabel = (runtime: any) => {
  return `${runtime.language} ${runtime.version}`
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    running: 'success',
    stopped: 'info',
    building: 'warning',
    deploying: 'warning',
    failed: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    building: '构建中',
    deploying: '部署中',
    failed: '失败'
  }
  return labelMap[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAppList()
})
</script>

<style lang="scss" scoped>
.app-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 8px 0;
      }
      
      .page-description {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
    }
  }
  
  .filter-section {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    
    .filter-left {
      display: flex;
      gap: 12px;
      flex: 1;
      
      .search-input {
        width: 300px;
      }
      
      .filter-select {
        width: 120px;
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        
        .search-input,
        .filter-select {
          width: 100%;
        }
      }
    }
    
    .filter-right {
      display: flex;
      gap: 8px;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
    }
  }
  
  .batch-actions {
    @include card-style;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 20px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-7);
    
    .batch-info {
      font-size: 14px;
      color: var(--el-color-primary);
    }
    
    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .app-list {
    @include card-style;
    padding: 0;
    
    .app-table {
      .app-info {
        display: flex;
        align-items: center;
        
        .app-avatar {
          width: 40px;
          height: 40px;
          background: var(--el-color-primary-light-8);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          
          .el-icon {
            font-size: 20px;
            color: var(--el-color-primary);
          }
        }
        
        .app-details {
          .app-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }
          
          .app-description {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            @include text-ellipsis;
            max-width: 200px;
          }
        }
      }
      
      .instance-count {
        font-weight: 500;
        color: var(--el-color-primary);
      }
      
      .resource-info {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        
        div {
          margin-bottom: 2px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .deploy-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
      
      .text-placeholder {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
      }
      
      :deep(.danger-item) {
        color: var(--el-color-danger);
      }
    }
    
    .pagination-container {
      padding: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
