<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-background">
      <div class="bg-decoration"></div>
    </div>
    
    <!-- 登录表单区域 -->
    <div class="login-form-container">
      <div class="login-form-wrapper">
        <!-- 平台Logo和标题 -->
        <div class="login-header">
          <img src="/logo.svg" alt="PaaS平台" class="login-logo" />
          <h1 class="login-title">PaaS 管理平台</h1>
          <p class="login-subtitle">企业级云原生应用管理平台</p>
        </div>
        
        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名或邮箱"
              :prefix-icon="User"
              clearable
              autocomplete="username"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              autocomplete="current-password"
            />
          </el-form-item>
          
          <!-- 验证码 (可选) -->
          <el-form-item v-if="showCaptcha" prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                :prefix-icon="Picture"
                class="captcha-input"
              />
              <img
                :src="captchaImage"
                alt="验证码"
                class="captcha-image"
                @click="refreshCaptcha"
              />
            </div>
          </el-form-item>
          
          <!-- 记住登录和忘记密码 -->
          <div class="login-options">
            <el-checkbox v-model="loginForm.remember">
              记住登录状态
            </el-checkbox>
            <el-button type="text" @click="showForgotPassword">
              忘记密码？
            </el-button>
          </div>
          
          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loginLoading"
              @click="handleLogin"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
          
          <!-- 第三方登录 (可选) -->
          <div class="oauth-login">
            <el-divider>
              <span class="oauth-divider-text">其他登录方式</span>
            </el-divider>
            <div class="oauth-buttons">
              <el-button circle @click="handleOAuthLogin('github')">
                <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 12.64c-282.752 0-512 229.216-512 512 0 226.208 146.688 418.144 350.08 485.824 25.6 4.736 35.008-11.104 35.008-24.64 0-12.192-0.48-52.544-0.704-95.328-142.464 30.976-172.512-60.416-172.512-60.416-23.296-59.168-56.832-74.912-56.832-74.912-46.464-31.776 3.52-31.136 3.52-31.136 51.392 3.616 78.464 52.768 78.464 52.768 45.664 78.272 119.776 55.648 148.992 42.56 4.576-33.088 17.856-55.68 32.512-68.48-113.728-12.928-233.28-56.864-233.28-253.024 0-55.904 19.936-101.568 52.672-137.408-5.312-12.896-22.848-64.96 4.96-135.488 0 0 42.88-13.76 140.8 52.48 40.832-11.36 84.64-17.024 128.16-17.248 43.488 0.192 87.328 5.888 128.256 17.248 97.728-66.24 140.64-52.48 140.64-52.48 27.872 70.528 10.336 122.592 5.024 135.488 32.832 35.84 52.608 81.504 52.608 137.408 0 196.64-119.776 239.936-233.792 252.64 18.368 15.904 34.72 47.04 34.72 94.816 0 68.512-0.608 123.648-0.608 140.512 0 13.632 9.216 29.6 35.168 24.576C877.472 942.08 1024 750.208 1024 524.64c0-282.784-229.248-512-512-512z"/></svg></el-icon>
              </el-button>
              <el-button circle @click="handleOAuthLogin('google')">
                <el-icon><svg viewBox="0 0 1024 1024"><path d="M881 442.4H519.7v148.5h206.4c-8.9 48-35.9 88.6-76.6 115.8-34.4 23-78.3 36.6-129.9 36.6-99.9 0-184.4-67.5-214.6-158.2-7.6-23-12-47.6-12-72.9s4.4-49.9 12-72.9c30.3-90.6 114.8-158.1 214.6-158.1 56.3 0 106.8 19.4 146.6 57.4l110-110.1c-66.5-62-153.2-100-256.6-100-149.9 0-279.6 86.13-342.7 211.4-26.5 52.86-41.5 112.9-41.5 176.6s15 123.74 41.5 176.6c63.1 125.3 192.8 211.4 342.7 211.4 117.6 0 217.6-43.5 289.6-116.1 72-72.7 112.4-181.2 112.4-299.7 0-28.2-2.4-55.8-6.9-82.5z"/></svg></el-icon>
              </el-button>
            </div>
          </div>
        </el-form>
        
        <!-- 页脚信息 -->
        <div class="login-footer">
          <p>&copy; 2024 PaaS 管理平台. 保留所有权利.</p>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-width="80px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册邮箱"
            :prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="forgotPasswordVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="resetPasswordLoading"
          @click="handleResetPassword"
        >
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock, Picture, Message } from '@element-plus/icons-vue'

// 响应式数据
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()
const forgotPasswordFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
})

// 忘记密码表单数据
const forgotPasswordForm = reactive({
  email: ''
})

// 状态管理
const loginLoading = ref(false)
const resetPasswordLoading = ref(false)
const forgotPasswordVisible = ref(false)
const showCaptcha = ref(false)
const captchaImage = ref('')

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

const forgotPasswordRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loginLoading.value = true
    
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    ElMessage.success('登录成功')
    
    // 跳转到目标页面或首页
    const redirect = route.query.redirect as string
    router.push(redirect || '/dashboard')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 如果是验证码错误，显示验证码
    if (error.response?.status === 429) {
      showCaptcha.value = true
      await refreshCaptcha()
    }
  } finally {
    loginLoading.value = false
  }
}

// 处理第三方登录
const handleOAuthLogin = (provider: string) => {
  // TODO: 实现第三方登录
  ElMessage.info(`${provider} 登录功能开发中...`)
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
}

// 处理重置密码
const handleResetPassword = async () => {
  if (!forgotPasswordFormRef.value) return
  
  try {
    await forgotPasswordFormRef.value.validate()
    resetPasswordLoading.value = true
    
    await authApi.resetPassword({
      email: forgotPasswordForm.email
    })
    
    ElMessage.success('重置密码邮件已发送，请查收邮箱')
    forgotPasswordVisible.value = false
    
  } catch (error) {
    console.error('重置密码失败:', error)
  } finally {
    resetPasswordLoading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    const response = await authApi.getCaptcha()
    captchaImage.value = response.data.image
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.login-background {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  }
  
  @media (max-width: 768px) {
    display: none;
  }
}

.login-form-container {
  width: 480px;
  background: var(--el-bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    width: 100%;
    padding: 20px;
  }
}

.login-form-wrapper {
  width: 100%;
  max-width: 360px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .login-logo {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }
  
  .login-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  .login-subtitle {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
    
    :deep(.el-input__inner) {
      height: 48px;
      line-height: 48px;
    }
  }
  
  .captcha-container {
    display: flex;
    gap: 12px;
    
    .captcha-input {
      flex: 1;
    }
    
    .captcha-image {
      width: 120px;
      height: 48px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      cursor: pointer;
      object-fit: cover;
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
  
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .el-button--text {
      padding: 0;
      font-size: 14px;
    }
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.oauth-login {
  margin-top: 32px;
  
  .oauth-divider-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  .oauth-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
    
    .el-button {
      width: 40px;
      height: 40px;
      
      .el-icon {
        font-size: 20px;
      }
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 40px;
  
  p {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-form-container {
    .login-header {
      margin-bottom: 30px;
      
      .login-title {
        font-size: 24px;
      }
    }
    
    .login-form {
      .el-form-item {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
