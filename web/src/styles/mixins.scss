// SCSS 混合宏定义

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 居中对齐
@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 垂直居中
@mixin vertical-center {
  display: flex;
  align-items: center;
}

// 水平居中
@mixin horizontal-center {
  display: flex;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
}

// 卡片样式
@mixin card-style {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  padding: 20px;
}

// 按钮悬停效果
@mixin button-hover($color) {
  transition: all $transition-duration $transition-function;
  
  &:hover {
    background-color: lighten($color, 10%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba($color, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 加载动画
@mixin loading-animation {
  @keyframes loading {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  animation: loading 1s linear infinite;
}

// 淡入动画
@mixin fade-in($duration: 0.3s) {
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  animation: fadeIn $duration ease-out;
}

// 滑入动画
@mixin slide-in($direction: 'left', $duration: 0.3s) {
  @keyframes slideIn {
    from {
      opacity: 0;
      @if $direction == 'left' {
        transform: translateX(-20px);
      } @else if $direction == 'right' {
        transform: translateX(20px);
      } @else if $direction == 'top' {
        transform: translateY(-20px);
      } @else if $direction == 'bottom' {
        transform: translateY(20px);
      }
    }
    to {
      opacity: 1;
      transform: translate(0, 0);
    }
  }
  
  animation: slideIn $duration ease-out;
}

// 状态指示器
@mixin status-indicator($color) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    width: 6px;
    height: 6px;
    background-color: $color;
    border-radius: 50%;
    transform: translateY(-50%);
  }
}
