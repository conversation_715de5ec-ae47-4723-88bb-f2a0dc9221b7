<template>
  <div class="loading-card" :class="{ 'is-loading': loading }">
    <!-- 加载状态遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content" :class="{ 'content-blur': loading }">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件属性定义
interface Props {
  loading?: boolean
  loadingText?: string
}

withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '加载中...'
})
</script>

<style lang="scss" scoped>
.loading-card {
  position: relative;
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
  transition: all 0.3s ease;
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .loading-content {
      text-align: center;
      
      .loading-icon {
        font-size: 32px;
        color: var(--el-color-primary);
        margin-bottom: 12px;
        @include loading-animation;
      }
      
      .loading-text {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .card-content {
    transition: filter 0.3s ease;
    
    &.content-blur {
      filter: blur(1px);
    }
  }
  
  &.is-loading {
    pointer-events: none;
  }
}

// 暗色主题适配
.dark {
  .loading-card {
    .loading-overlay {
      background: rgba(0, 0, 0, 0.6);
    }
  }
}
</style>
