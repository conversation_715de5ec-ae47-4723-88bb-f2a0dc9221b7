设计一个 paas 平台, 先完成上层架构的设计:

- 实现应用管理层, 通过定制一套应用规范, 为后续的具体实现奠定基础. 然后实现具体的 nodejs, python 等应用
- 实现 ci/cd 层, 通过定制一套 ci/cd 规范, 为后续的具体实现奠定基础.
  - 实现 gitea 作为代码仓库
  - 实现 docker 作为构建环境
  - 其他 ci/cd 具体的功能实现
- 实现数据存储层, 通过定制一套数据存储规范, 为后续的具体实现奠定基础. 需要兼容多种数据库, 例如 sqlite, pgsql 等
- 其他功能的顶层设计分析和具体实现的建议

请为我设计并实现一个完整的 PaaS（Platform as a Service）平台架构。请按照以下具体要求进行分层设计和实现：

## 1. 应用管理层设计与实现
- 设计一套标准化的应用规范文档，包括：
  - 应用配置文件格式（如 app.yaml 或 paas.json）
  - 应用生命周期管理规范（部署、启动、停止、更新、回滚）
  - 资源限制和环境变量配置规范
  - 健康检查和监控接口规范
- 实现应用管理核心模块，支持：
  - Node.js 应用的部署和管理
  - Python 应用的部署和管理
  - 应用实例的动态扩缩容
  - 应用版本管理和回滚机制
- 提供 RESTful API 接口用于应用管理操作

## 2. CI/CD 层设计与实现
- 设计 CI/CD 流水线规范，包括：
  - 构建配置文件格式（如 .paas-ci.yaml）
  - 构建阶段定义（代码检出、依赖安装、测试、构建、部署）
  - 环境变量和密钥管理规范
- 集成和配置以下组件：
  - **Gitea**: 作为代码仓库，配置 webhook 触发构建
  - **Docker**: 作为容器化构建和运行环境
  - **构建调度器**: 管理构建任务队列和执行
- 实现具体功能：
  - 自动化构建流水线
  - 多环境部署（开发、测试、生产）
  - 构建日志和状态管理
  - 部署策略（蓝绿部署、滚动更新）

## 3. 数据存储层设计与实现
- 设计数据存储抽象层，包括：
  - 数据库连接池管理
  - ORM/查询构建器抽象接口
  - 数据迁移和版本管理机制
- 实现多数据库支持：
  - SQLite（用于开发和轻量级部署）
  - PostgreSQL（用于生产环境）
  - 数据库适配器模式，便于扩展其他数据库
- 提供数据备份和恢复功能

## 4. 其他核心功能设计
请分析并提供以下功能的顶层设计和实现建议：
- **用户认证与权限管理**: RBAC 权限模型，支持多租户
- **监控与日志系统**: 应用性能监控、日志聚合和分析
- **负载均衡与服务发现**: 内置负载均衡器和服务注册发现
- **配置管理**: 集中化配置管理和热更新
- **安全机制**: 网络隔离、密钥管理、安全扫描

## 实现要求
- 使用 Go 或 Python 作为主要开发语言
- 采用微服务架构，各模块可独立部署
- 提供完整的 API 文档和使用说明
- 包含单元测试和集成测试
- 使用 Docker 容器化部署
- 提供 Web 管理界面（可选）

请先从整体架构设计开始，然后逐步实现各个模块，并为每个模块提供详细的中文文档和代码注释。

---

实现前端设计:

- 界面设计美观大方
- 操作简便

实现前端用户界面设计，具体要求如下：

**界面设计要求：**
- 采用现代化的UI设计风格，界面布局清晰合理
- 使用统一的色彩搭配和字体规范
- 确保界面元素对齐整齐，间距适中
- 支持响应式设计，适配不同屏幕尺寸
- 遵循用户体验最佳实践

**操作体验要求：**
- 用户操作流程简洁直观，减少不必要的步骤
- 提供清晰的操作反馈和状态提示
- 重要操作需要确认机制，防止误操作
- 加载状态和错误信息要有明确的视觉提示
- 支持键盘快捷键操作提高效率

**技术实现要求：**
- 选择合适的前端框架和UI组件库
- 确保代码结构清晰，组件可复用
- 添加详细的中文注释说明关键逻辑
- 编写相应的单元测试验证功能正确性

---

根据当前项目的技术架构, 对项目进行优化:

- 补充 .gitignore
- 缓存 redis 没有配置则禁用 redis
- 可以配置禁用 jwt 校验

请根据当前项目的技术架构对项目进行以下三个方面的优化，每个优化都需要提供详细的实现方案和配置说明：

1. **补充和完善 .gitignore 文件**
   - 分析当前项目的技术栈和目录结构
   - 添加适合该项目类型的忽略规则（如依赖目录、构建产物、日志文件、配置文件等）
   - 确保不会意外提交敏感信息或不必要的文件

2. **实现 Redis 缓存的条件启用机制**
   - 检测 Redis 配置是否存在和有效
   - 当 Redis 未配置或连接失败时，自动禁用 Redis 缓存功能
   - 提供优雅的降级机制，确保应用在没有 Redis 的情况下仍能正常运行
   - 添加相关的配置选项和错误处理逻辑

3. **实现 JWT 校验的可配置开关**
   - 添加配置选项来控制是否启用 JWT 校验
   - 在开发环境或特定场景下可以禁用 JWT 校验
   - 确保禁用 JWT 校验时的安全性考虑和替代方案
   - 提供清晰的配置文档和使用说明

请为每个优化提供：
- 具体的代码实现
- 配置文件的修改
- 详细的中文注释说明
- 相关的单元测试（如适用）
- 使用文档说明

---

根据当前的设计, 怎么实现快速部署一个 python 脚本:

- 创建一个应用
- 将脚本上传到应用对应的 git 仓库
- 接收 git webhook 消息
- 构建应用并部署
- 外部调用应用暴露的接口实现启动容器并接收接口的参数, 然后计算完成后返回结果
- 外部接收到返回的结果
- 自动销毁容器回收资源

请根据当前的代码实现确认是否能处理该需求, 如果不行, 请解释并处理:

- 当前是否有更优的设计完成上述需求的实现
- 是否有更优的实现, 分析原因并进行实现

请分析当前PaaS平台的架构设计，评估其是否能够支持以下Python脚本快速部署和执行的完整工作流：

**目标工作流程：**
1. **应用创建**：通过平台创建一个新的Python应用实例
2. **代码部署**：将Python脚本上传到应用对应的Git仓库
3. **自动构建**：接收Git webhook触发事件，自动启动构建流程
4. **应用部署**：构建完成后自动部署应用到容器环境
5. **接口调用**：外部系统通过HTTP API调用应用，传递执行参数
6. **动态执行**：容器启动并执行Python脚本，处理传入的参数
7. **结果返回**：脚本执行完成后返回计算结果给调用方
8. **资源回收**：执行完成后自动销毁容器，释放系统资源

**具体分析要求：**
1. **现状评估**：
   - 检查当前代码库中的应用管理、Git集成、构建部署、容器编排等模块
   - 确认现有架构是否已支持上述工作流的各个环节
   - 识别缺失的功能组件和潜在的技术障碍

2. **优化建议**：
   - 如果当前设计无法完全满足需求，请提出更优的架构方案
   - 分析现有设计的不足之处和改进空间
   - 考虑性能、可扩展性、资源利用率等因素

3. **实现方案**：
   - 如果需要改进，请提供具体的实现计划和代码修改建议
   - 包括新增模块、修改现有组件、API设计等
   - 确保方案符合云原生和微服务架构最佳实践

请先通过代码检索工具了解当前系统的整体架构，然后提供详细的分析报告和实现建议。

---